{"abi": [{"type": "function", "name": "claimableDepositRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "claimableAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "isOperator", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "status", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "pendingDepositRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "pendingAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "requestDeposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "DepositRequest", "inputs": [{"name": "controller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "sender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OperatorSet", "inputs": [{"name": "controller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"claimableDepositRequest(uint256,address)": "995ea21a", "deposit(uint256,address,address)": "2e2d2984", "isOperator(address,address)": "b6363cf2", "mint(uint256,address,address)": "da39b3e7", "pendingDepositRequest(uint256,address)": "26c6f96c", "requestDeposit(uint256,address,address)": "85b77f45", "setOperator(address,bool)": "558a7297"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"DepositRequest\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"OperatorSet\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"claimableDepositRequest\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"claimableAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"mint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"pendingDepositRequest\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"pendingAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"requestDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Interface of the asynchronous deposit Vault interface of ERC7540, as defined in https://eips.ethereum.org/EIPS/eip-7540\",\"events\":{\"OperatorSet(address,address,bool)\":{\"details\":\"The event emitted when an operator is set.\",\"params\":{\"approved\":\"The approval status.\",\"controller\":\"The address of the controller.\",\"operator\":\"The address of the operator.\"}}},\"kind\":\"dev\",\"methods\":{\"claimableDepositRequest(uint256,address)\":{\"details\":\"Returns the amount of requested assets in Claimable state for the controller to deposit or mint. - MUST NOT include any assets in Pending state. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input.\"},\"deposit(uint256,address,address)\":{\"details\":\"Mints shares Vault shares to receiver by claiming the Request of the controller. - MUST emit the Deposit event. - controller MUST equal msg.sender unless the controller has approved the msg.sender as an operator.\"},\"isOperator(address,address)\":{\"details\":\"Returns `true` if the `operator` is approved as an operator for an `controller`.\",\"params\":{\"controller\":\"The address of the controller.\",\"operator\":\"The address of the operator.\"},\"returns\":{\"status\":\"The approval status\"}},\"mint(uint256,address,address)\":{\"details\":\"Mints exactly shares Vault shares to receiver by claiming the Request of the controller. - MUST emit the Deposit event. - controller MUST equal msg.sender unless the controller has approved the msg.sender as an operator.\"},\"pendingDepositRequest(uint256,address)\":{\"details\":\"Returns the amount of requested assets in Pending state. - MUST NOT include any assets in Claimable state for deposit or mint. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input.\"},\"requestDeposit(uint256,address,address)\":{\"details\":\"Transfers assets from sender into the Vault and submits a Request for asynchronous deposit. - MUST support ERC-20 approve / transferFrom on asset as a deposit Request flow. - MUST revert if all of assets cannot be requested for deposit. - owner MUST be msg.sender unless some unspecified explicit approval is given by the caller,    approval of ERC-20 tokens from owner to sender is NOT enough.\",\"params\":{\"assets\":\"the amount of deposit assets to transfer from owner\",\"controller\":\"the controller of the request who will be able to operate the request\",\"owner\":\"the source of the deposit assets NOTE: most implementations will require pre-approval of the Vault with the Vault's underlying asset token.\"}},\"setOperator(address,bool)\":{\"details\":\"Sets or removes an operator for the caller.\",\"params\":{\"approved\":\"The approval status.\",\"operator\":\"The address of the operator.\"},\"returns\":{\"_0\":\"Whether the call was executed successfully or not\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/interfaces/IERC7540.sol\":\"IERC7540Deposit\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/interfaces/IERC165.sol\":{\"keccak256\":\"0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc\",\"dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT\"]},\"lib/forge-std/src/interfaces/IERC7540.sol\":{\"keccak256\":\"0x9e130f02e48f3a015fd9e41428b1c8fb359bb7193bba4bd97f5e068af5903025\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://67ebd6a0f3d0197aa74fe905b3135bd555a387bbc9d53975b1bd2134644dbf94\",\"dweb:/ipfs/QmfJytpVRmpytBunkrGLiugW3tPBZn12A3Ak9LAcV6Tj3m\"]},\"lib/forge-std/src/interfaces/IERC7575.sol\":{\"keccak256\":\"0xb46aebdd749e632cf76466d2f75428f8e41e8283145818b621acd2624793782c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b32be8816d4658db66fdca29e9a9bc3a071bc47da19d6afa950eec5a32781ccb\",\"dweb:/ipfs/QmbJ27FAJz4rxV2xTzEPHkihYRrvhHH9mdkNSdxvba5Zxf\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "controller", "type": "address", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "requestId", "type": "uint256", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}], "type": "event", "name": "DepositRequest", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address", "indexed": true}, {"internalType": "address", "name": "operator", "type": "address", "indexed": true}, {"internalType": "bool", "name": "approved", "type": "bool", "indexed": false}], "type": "event", "name": "OperatorSet", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "view", "type": "function", "name": "claimableDepositRequest", "outputs": [{"internalType": "uint256", "name": "claimableAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isOperator", "outputs": [{"internalType": "bool", "name": "status", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "mint", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "view", "type": "function", "name": "pendingDepositRequest", "outputs": [{"internalType": "uint256", "name": "pendingAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "requestDeposit", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"claimableDepositRequest(uint256,address)": {"details": "Returns the amount of requested assets in Claimable state for the controller to deposit or mint. - MUST NOT include any assets in Pending state. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input."}, "deposit(uint256,address,address)": {"details": "Mints shares Vault shares to receiver by claiming the Request of the controller. - MUST emit the Deposit event. - controller MUST equal msg.sender unless the controller has approved the msg.sender as an operator."}, "isOperator(address,address)": {"details": "Returns `true` if the `operator` is approved as an operator for an `controller`.", "params": {"controller": "The address of the controller.", "operator": "The address of the operator."}, "returns": {"status": "The approval status"}}, "mint(uint256,address,address)": {"details": "Mints exactly shares Vault shares to receiver by claiming the Request of the controller. - MUST emit the Deposit event. - controller MUST equal msg.sender unless the controller has approved the msg.sender as an operator."}, "pendingDepositRequest(uint256,address)": {"details": "Returns the amount of requested assets in Pending state. - MUST NOT include any assets in Claimable state for deposit or mint. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input."}, "requestDeposit(uint256,address,address)": {"details": "Transfers assets from sender into the Vault and submits a Request for asynchronous deposit. - MUST support ERC-20 approve / transferFrom on asset as a deposit Request flow. - MUST revert if all of assets cannot be requested for deposit. - owner MUST be msg.sender unless some unspecified explicit approval is given by the caller,    approval of ERC-20 tokens from owner to sender is NOT enough.", "params": {"assets": "the amount of deposit assets to transfer from owner", "controller": "the controller of the request who will be able to operate the request", "owner": "the source of the deposit assets NOTE: most implementations will require pre-approval of the Vault with the Vault's underlying asset token."}}, "setOperator(address,bool)": {"details": "Sets or removes an operator for the caller.", "params": {"approved": "The approval status.", "operator": "The address of the operator."}, "returns": {"_0": "Whether the call was executed successfully or not"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/interfaces/IERC7540.sol": "IERC7540Deposit"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"lib/forge-std/src/interfaces/IERC165.sol": {"keccak256": "0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600", "urls": ["bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc", "dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT"], "license": "MIT"}, "lib/forge-std/src/interfaces/IERC7540.sol": {"keccak256": "0x9e130f02e48f3a015fd9e41428b1c8fb359bb7193bba4bd97f5e068af5903025", "urls": ["bzz-raw://67ebd6a0f3d0197aa74fe905b3135bd555a387bbc9d53975b1bd2134644dbf94", "dweb:/ipfs/QmfJytpVRmpytBunkrGLiugW3tPBZn12A3Ak9LAcV6Tj3m"], "license": "MIT"}, "lib/forge-std/src/interfaces/IERC7575.sol": {"keccak256": "0xb46aebdd749e632cf76466d2f75428f8e41e8283145818b621acd2624793782c", "urls": ["bzz-raw://b32be8816d4658db66fdca29e9a9bc3a071bc47da19d6afa950eec5a32781ccb", "dweb:/ipfs/QmbJ27FAJz4rxV2xTzEPHkihYRrvhHH9mdkNSdxvba5Zxf"], "license": "MIT"}}, "version": 1}, "id": 25}