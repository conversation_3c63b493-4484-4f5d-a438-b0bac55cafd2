"""
LLM-generated invariants with SMT proof verification.

Uses OpenAI API to generate invariants and Z3/CVC5 for formal verification.
"""

import json
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import openai
from rich.console import Console
from rich.progress import Progress

from .models import InvariantResults, Invariant, FunctionSignature, SolverType
from .contract_analyzer import ContractAnalyzer
from .smt_translator import SMTTranslator

console = Console()


class InvariantChecker:
    """Generates and verifies invariants using LLM + SMT solvers."""
    
    def __init__(
        self,
        contract_path: Path,
        openai_key: Optional[str] = None,
        solver: str = "z3",
        output_dir: Optional[Path] = None,
        verbose: bool = False,
    ):
        self.contract_path = contract_path
        self.solver = SolverType(solver)
        self.output_dir = output_dir or Path("./output")
        self.verbose = verbose
        
        # Setup OpenAI client
        api_key = openai_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key required. Set OPENAI_API_KEY or pass --openai-key")
        
        self.openai_client = openai.OpenAI(api_key=api_key)
        
        # Initialize components
        self.analyzer = ContractAnalyzer(verbose=verbose)
        self.smt_translator = SMTTranslator(solver=self.solver, verbose=verbose)
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def run(self) -> InvariantResults:
        """Execute the complete invariant checking process."""
        start_time = time.time()
        
        if self.verbose:
            console.print(f"🧠 Starting invariant analysis of {self.contract_path}")
        
        # Analyze contract to get function signatures
        contract_analysis = self.analyzer.analyze_contract(self.contract_path)
        if not contract_analysis.is_valid:
            raise RuntimeError(f"Contract analysis failed: {contract_analysis.analysis_errors}")
        
        # Get functions suitable for invariant generation
        target_functions = [
            func for func in contract_analysis.functions
            if func.visibility in ["public", "external"]
        ]
        
        if not target_functions:
            console.print("⚠️  No suitable functions found for invariant generation")
            return InvariantResults(
                contract_path=self.contract_path,
                total_invariants=0,
                execution_time=time.time() - start_time
            )
        
        if self.verbose:
            console.print(f"Generating invariants for {len(target_functions)} functions")
        
        # Generate invariants for each function
        all_invariants = []
        for func in target_functions:
            if self.verbose:
                console.print(f"Generating invariants for: {func.signature}")
            
            func_invariants = self._generate_function_invariants(func, contract_analysis)
            all_invariants.extend(func_invariants)
        
        if self.verbose:
            console.print(f"Generated {len(all_invariants)} total invariants")
        
        # Verify invariants using SMT solver
        results = InvariantResults(
            contract_path=self.contract_path,
            total_invariants=len(all_invariants),
            execution_time=0.0
        )
        
        if all_invariants:
            results = self._verify_invariants(all_invariants, results)
        
        results.execution_time = time.time() - start_time
        
        # Save results
        self._save_results(results)
        
        return results
    
    def _generate_function_invariants(self, func: FunctionSignature, contract_analysis) -> List[Invariant]:
        """Generate invariants for a specific function using OpenAI."""
        try:
            # Read contract source for context
            contract_source = self._get_contract_source(contract_analysis.contract_path)
            
            # Create prompt for LLM
            prompt = self._create_invariant_prompt(func, contract_source)
            
            # Call OpenAI API
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert in Solidity smart contract security and formal verification. Generate precise invariants for smart contract functions."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,  # Low temperature for consistent results
                max_tokens=2000
            )
            
            # Parse response to extract invariants
            invariants_text = response.choices[0].message.content
            return self._parse_invariants_response(invariants_text, func.name)
            
        except Exception as e:
            if self.verbose:
                console.print(f"❌ Failed to generate invariants for {func.name}: {e}")
            return []
    
    def _create_invariant_prompt(self, func: FunctionSignature, contract_source: str) -> str:
        """Create a prompt for LLM to generate invariants."""
        prompt = f"""
Analyze the following Solidity function and generate 10 invariants (preconditions and postconditions) that must hold.

Contract source:
```solidity
{contract_source[:2000]}  // Truncated for brevity
```

Function to analyze:
```solidity
function {func.signature}
```

Function details:
- Name: {func.name}
- Visibility: {func.visibility}
- State Mutability: {func.stateMutability}
- Parameters: {[f"{p['type']} {p['name']}" for p in func.inputs]}
- Returns: {[f"{p['type']} {p['name']}" for p in func.outputs]}

Please provide exactly 10 invariants in the following JSON format:
```json
[
  {{
    "type": "precondition",
    "description": "Brief description of what this invariant checks",
    "assertion": "require(condition, 'error message');"
  }},
  {{
    "type": "postcondition", 
    "description": "Brief description of what this invariant checks",
    "assertion": "assert(condition);"
  }}
]
```

Focus on:
1. Input validation (bounds, non-zero checks, etc.)
2. State consistency (balances, ownership, etc.)
3. Mathematical properties (overflow, underflow)
4. Business logic constraints
5. Access control requirements

Make assertions concrete and executable in Solidity.
"""
        return prompt
    
    def _parse_invariants_response(self, response_text: str, function_name: str) -> List[Invariant]:
        """Parse LLM response to extract invariants."""
        invariants = []
        
        try:
            # Extract JSON from response
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(1)
                invariants_data = json.loads(json_text)
                
                for i, inv_data in enumerate(invariants_data):
                    invariant = Invariant(
                        id=f"{function_name}_{i}",
                        function_name=function_name,
                        type=inv_data.get("type", "unknown"),
                        description=inv_data.get("description", ""),
                        solidity_assertion=inv_data.get("assertion", "")
                    )
                    invariants.append(invariant)
            
        except Exception as e:
            if self.verbose:
                console.print(f"Failed to parse invariants response: {e}")
        
        return invariants
    
    def _verify_invariants(self, invariants: List[Invariant], results: InvariantResults) -> InvariantResults:
        """Verify invariants using SMT solver."""
        with Progress() as progress:
            task = progress.add_task("Verifying invariants", total=len(invariants))
            
            for invariant in invariants:
                try:
                    # Translate Solidity assertion to SMT
                    smt_formula = self.smt_translator.translate_assertion(invariant.solidity_assertion)
                    invariant.smt_formula = smt_formula
                    
                    if smt_formula:
                        # Check with SMT solver
                        is_verified, counterexample = self.smt_translator.verify_formula(smt_formula)
                        invariant.is_verified = is_verified
                        invariant.counterexample = counterexample
                        
                        if is_verified:
                            results.verified_invariants.append(invariant)
                        else:
                            results.violations.append(invariant)
                    else:
                        # Translation failed
                        results.errors.append(invariant)
                        
                except Exception as e:
                    if self.verbose:
                        console.print(f"Error verifying invariant {invariant.id}: {e}")
                    invariant.is_verified = None
                    results.errors.append(invariant)
                
                progress.advance(task)
        
        return results
    
    def _get_contract_source(self, contract_path: Path) -> str:
        """Get contract source code for context."""
        try:
            if contract_path.is_file():
                return contract_path.read_text()
            else:
                # For directories, find main contract
                sol_files = list(contract_path.glob("**/*.sol"))
                if sol_files:
                    return sol_files[0].read_text()
                return ""
        except Exception:
            return ""
    
    def _save_results(self, results: InvariantResults) -> None:
        """Save invariant results to JSON file."""
        output_file = self.output_dir / "invariant_results.json"
        
        results_dict = {
            "contract_path": str(results.contract_path),
            "total_invariants": results.total_invariants,
            "verified_invariants": [inv.to_dict() for inv in results.verified_invariants],
            "violations": [inv.to_dict() for inv in results.violations],
            "errors": [inv.to_dict() for inv in results.errors],
            "execution_time": results.execution_time,
            "verification_rate": results.verification_rate,
            "has_violations": results.has_violations,
        }
        
        with open(output_file, "w") as f:
            json.dump(results_dict, f, indent=2)
        
        if self.verbose:
            console.print(f"💾 Invariant results saved to {output_file}")
