#!/usr/bin/env python3
"""
Example of how to run fuzzing directly using the Fuzz<PERSON>unner class.
"""

from pathlib import Path
import sys
import os

# Add the parent directory to Python path to import smart_bug_hunter
sys.path.insert(0, str(Path(__file__).parent.parent))

from smart_bug_hunter.fuzz import <PERSON><PERSON><PERSON><PERSON><PERSON>

def main():
    """Run fuzzing on the Bank contract."""
    
    # Set up paths
    contract_path = Path("Bank.sol")
    schema_path = Path("schema.json")
    output_dir = Path("output")
    
    # Check if contract exists
    if not contract_path.exists():
        print(f"❌ Contract not found: {contract_path}")
        return 1
    
    # Create fuzzer instance
    fuzzer = FuzzRunner(
        contract_path=contract_path,
        schema_path=schema_path,
        strength=2,  # Pairwise testing
        output_dir=output_dir,
        verbose=True
    )
    
    try:
        # Run the fuzzing
        print("🔍 Starting fuzzing analysis...")
        results = fuzzer.run()
        
        # Print results
        print(f"\n📊 Fuzzing Results:")
        print(f"Total pools: {results.total_pools}")
        print(f"Passed: {len(results.passed_pools)}")
        print(f"Failed: {len(results.failed_pools)}")
        print(f"Errors: {len(results.error_pools)}")
        print(f"Success rate: {results.success_rate:.2%}")
        print(f"Execution time: {results.execution_time:.2f}s")
        
        if results.has_failures:
            print("\n❌ Found potential bugs!")
            for pool in results.failed_pools[:3]:  # Show first 3 failures
                params = dict(zip(pool.parameter_names, pool.parameters))
                print(f"  Pool {pool.id}: {params}")
                if pool.error_message:
                    print(f"    Error: {pool.error_message[:100]}...")
            return 1
        else:
            print("\n✅ No bugs found!")
            return 0
            
    except Exception as e:
        print(f"❌ Fuzzing failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
