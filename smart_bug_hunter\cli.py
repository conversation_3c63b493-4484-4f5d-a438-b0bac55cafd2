"""
CLI entry point for smart-bug-hunter.

Provides commands for fuzzing, invariant checking, and combined analysis.
"""

import sys
from pathlib import Path
from typing import Optional, List
import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from .fuzz import FuzzRunner
from .invariants import Invariant<PERSON>he<PERSON>
from .report import ReportGenerator

app = typer.Typer(
    name="smart-bug-hunter",
    help="Combinatorial fuzzing and formal verification for Solidity contracts",
    add_completion=False,
)
console = Console()


@app.command()
def fuzz(
    contract_path: Path = typer.Argument(..., help="Path to Solidity contract or project directory"),
    schema_path: Optional[Path] = typer.Option(None, "--schema", "-s", help="JSON schema file for function inputs"),
    strength: int = typer.Option(2, "--strength", "-t", help="T-wise testing strength (default: pairwise=2)"),
    output: Optional[Path] = typer.Option(None, "--output", "-o", help="Output directory for results"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
) -> None:
    """Run combinatorial fuzzing on Solidity contracts using Foundry."""
    console.print(Panel.fit("🔍 Smart Bug Hunter - Fuzzing Mode", style="bold blue"))
    
    if not contract_path.exists():
        console.print(f"❌ Contract path not found: {contract_path}", style="red")
        raise typer.Exit(1)
    
    try:
        runner = FuzzRunner(
            contract_path=contract_path,
            schema_path=schema_path,
            strength=strength,
            output_dir=output,
            verbose=verbose
        )
        
        results = runner.run()
        
        if results.has_failures:
            console.print("❌ Fuzzing found potential bugs!", style="red")
            console.print(f"Failed pools: {len(results.failed_pools)}")
            sys.exit(1)
        else:
            console.print("✅ No bugs found in fuzzing", style="green")
            
    except Exception as e:
        console.print(f"❌ Fuzzing failed: {e}", style="red")
        if verbose:
            console.print_exception()
        raise typer.Exit(1)


@app.command()
def prove(
    contract_path: Path = typer.Argument(..., help="Path to Solidity contract or project directory"),
    openai_key: Optional[str] = typer.Option(None, "--openai-key", help="OpenAI API key (or set OPENAI_API_KEY)"),
    solver: str = typer.Option("z3", "--solver", help="SMT solver to use (z3 or cvc5)"),
    output: Optional[Path] = typer.Option(None, "--output", "-o", help="Output directory for results"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
) -> None:
    """Generate and verify invariants using LLM + SMT solvers."""
    console.print(Panel.fit("🧠 Smart Bug Hunter - Invariant Verification", style="bold purple"))
    
    if not contract_path.exists():
        console.print(f"❌ Contract path not found: {contract_path}", style="red")
        raise typer.Exit(1)
    
    try:
        checker = InvariantChecker(
            contract_path=contract_path,
            openai_key=openai_key,
            solver=solver,
            output_dir=output,
            verbose=verbose
        )
        
        results = checker.run()
        
        if results.has_violations:
            console.print("❌ Invariant violations found!", style="red")
            console.print(f"Violated invariants: {len(results.violations)}")
            sys.exit(1)
        else:
            console.print("✅ All invariants verified", style="green")
            
    except Exception as e:
        console.print(f"❌ Invariant checking failed: {e}", style="red")
        if verbose:
            console.print_exception()
        raise typer.Exit(1)


@app.command()
def all(
    contract_path: Path = typer.Argument(..., help="Path to Solidity contract or project directory"),
    schema_path: Optional[Path] = typer.Option(None, "--schema", "-s", help="JSON schema file for function inputs"),
    openai_key: Optional[str] = typer.Option(None, "--openai-key", help="OpenAI API key (or set OPENAI_API_KEY)"),
    strength: int = typer.Option(2, "--strength", "-t", help="T-wise testing strength (default: pairwise=2)"),
    solver: str = typer.Option("z3", "--solver", help="SMT solver to use (z3 or cvc5)"),
    output: Optional[Path] = typer.Option(None, "--output", "-o", help="Output directory for results"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose output"),
) -> None:
    """Run complete analysis: fuzzing + invariant verification + unified report."""
    console.print(Panel.fit("🚀 Smart Bug Hunter - Complete Analysis", style="bold green"))
    
    if not contract_path.exists():
        console.print(f"❌ Contract path not found: {contract_path}", style="red")
        raise typer.Exit(1)
    
    bugs_found = False
    
    try:
        # Run fuzzing
        console.print("\n📊 Phase 1: Combinatorial Fuzzing", style="bold cyan")
        fuzz_runner = FuzzRunner(
            contract_path=contract_path,
            schema_path=schema_path,
            strength=strength,
            output_dir=output,
            verbose=verbose
        )
        fuzz_results = fuzz_runner.run()
        
        if fuzz_results.has_failures:
            bugs_found = True
            console.print(f"⚠️  Found {len(fuzz_results.failed_pools)} failing test pools", style="yellow")
        
        # Run invariant checking
        console.print("\n🧠 Phase 2: Invariant Verification", style="bold cyan")
        invariant_checker = InvariantChecker(
            contract_path=contract_path,
            openai_key=openai_key,
            solver=solver,
            output_dir=output,
            verbose=verbose
        )
        invariant_results = invariant_checker.run()
        
        if invariant_results.has_violations:
            bugs_found = True
            console.print(f"⚠️  Found {len(invariant_results.violations)} invariant violations", style="yellow")
        
        # Generate unified report
        console.print("\n📋 Phase 3: Report Generation", style="bold cyan")
        report_gen = ReportGenerator(output_dir=output, verbose=verbose)
        report_path = report_gen.generate_unified_report(fuzz_results, invariant_results)
        
        console.print(f"\n📄 Report saved to: {report_path}", style="bold")
        
        if bugs_found:
            console.print("\n❌ Analysis complete - BUGS FOUND!", style="bold red")
            sys.exit(1)
        else:
            console.print("\n✅ Analysis complete - No bugs detected", style="bold green")
            
    except Exception as e:
        console.print(f"❌ Analysis failed: {e}", style="red")
        if verbose:
            console.print_exception()
        raise typer.Exit(1)


@app.command()
def version() -> None:
    """Show version information."""
    from . import __version__
    console.print(f"smart-bug-hunter version {__version__}")


if __name__ == "__main__":
    app()
