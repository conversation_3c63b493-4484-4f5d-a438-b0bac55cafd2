// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_batchDeposit_3 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_batchDeposit_pool_3() public {
        // Test pool 3: {'amounts': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]}
        try target.batchDeposit([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}