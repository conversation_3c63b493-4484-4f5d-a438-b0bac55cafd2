# Smart Bug Hunter 🔍

**Combinatorial fuzzing and formal verification for Solidity smart contracts**

Find bugs faster with group-testing fuzzing + LLM-generated invariants + SMT proofs.

## Quick Start

```bash
pip install smart-bug-hunter
smart-bug-hunter all MyContract.sol
```

That's it. One command finds bugs in your Solidity contracts.

## What It Does

Smart Bug Hunter combines three powerful techniques:

1. **🎯 Combinatorial Fuzzing** - Tests parameter combinations using group-testing theory (finds bugs with ~100 tests instead of millions)
2. **🧠 LLM Invariants** - GPT-4o generates formal specifications for your functions automatically  
3. **⚡ SMT Verification** - Z3/CVC5 proves or disproves each invariant with mathematical certainty

## Installation

**Prerequisites:**
- Python 3.11+
- [Foundry](https://foundry.paradigm.xyz) (`curl -L https://foundry.paradigm.xyz | bash && foundryup`)
- OpenAI API key (for invariant generation)

```bash
pip install smart-bug-hunter
```

## Usage

### Complete Analysis
```bash
export OPENAI_API_KEY="your-key-here"
smart-bug-hunter all MyContract.sol
```

### Fuzzing Only
```bash
smart-bug-hunter fuzz MyContract.sol --schema inputs.json
```

### Invariant Verification Only
```bash
smart-bug-hunter prove MyContract.sol --openai-key YOUR_KEY
```

## Demo

Try it on a vulnerable contract:

```bash
git clone https://github.com/smart-bug-hunter/smart-bug-hunter
cd smart-bug-hunter/demo
./run_demo.sh
```

Expected output: Finds 6+ vulnerabilities in 30 seconds.

## How It Works

### 1. Combinatorial Fuzzing
Instead of random testing, uses **AllPairs** algorithm to generate minimal test suites:
- Pairwise testing: covers all 2-way parameter interactions
- T-wise testing: covers all t-way interactions (configurable)
- Reduces millions of test cases to dozens while maintaining coverage

### 2. LLM Invariant Generation  
Sends your contract to GPT-4o with a specialized prompt:
- Generates 10 pre/post conditions per function
- Outputs executable Solidity assertions
- Covers input validation, state consistency, mathematical properties

### 3. SMT Formal Verification
Translates invariants to SMT-LIB and feeds to Z3/CVC5:
- **SAT result** = invariant can be violated (bug found + counterexample)
- **UNSAT result** = invariant always holds (verified)
- **Unknown** = solver timeout (inconclusive)

## Input Schema

Define test values in JSON:

```json
{
  "transfer": {
    "to": ["0x0000000000000000000000000000000000000000", "0x1111111111111111111111111111111111111111"],
    "amount": [0, 1, 1000, 115792089237316195423570985008687907853269984665640564039457584007913129639935]
  }
}
```

Without schema, uses smart defaults based on Solidity types.

## Commands

| Command | Description |
|---------|-------------|
| `fuzz` | Run combinatorial fuzzing only |
| `prove` | Run invariant verification only |
| `all` | Run complete analysis + unified report |
| `version` | Show version info |

## Options

| Flag | Description | Default |
|------|-------------|---------|
| `--schema` | JSON input schema file | Auto-generated |
| `--strength` | T-wise testing strength | 2 (pairwise) |
| `--solver` | SMT solver (z3/cvc5) | z3 |
| `--output` | Output directory | ./output |
| `--verbose` | Detailed logging | False |

## Output

Generates unified Markdown report with:
- **Executive Summary** - High-level findings
- **Failing Test Pools** - Minimal parameter sets that cause failures  
- **Violated Invariants** - Formal specifications that don't hold + counterexamples
- **Recommendations** - Specific fixes for each issue

## Architecture

```
smart_bug_hunter/
├── cli.py              # Typer-based CLI
├── fuzz.py             # Combinatorial fuzzing with AllPairs
├── invariants.py       # OpenAI integration + LLM prompts
├── smt_translator.py   # Solidity → SMT-LIB translation
├── contract_analyzer.py # Slither integration
├── report.py           # Markdown report generation
└── models.py           # Data structures
```

## Dependencies

- **Core:** `typer`, `allpairspy`, `openai`, `z3-solver`, `slither-analyzer`, `rich`
- **External:** Foundry (forge/cast), Z3 or CVC5 solver

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality  
4. Run the test suite: `pytest`
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) file.

## Citation

If you use Smart Bug Hunter in research, please cite:

```bibtex
@software{smart_bug_hunter,
  title={Smart Bug Hunter: Combinatorial Fuzzing and Formal Verification for Solidity},
  author={Smart Bug Hunter Team},
  year={2024},
  url={https://github.com/smart-bug-hunter/smart-bug-hunter}
}
```

## Support

- 📖 [Documentation](https://smart-bug-hunter.readthedocs.io)
- 🐛 [Issue Tracker](https://github.com/smart-bug-hunter/smart-bug-hunter/issues)
- 💬 [Discussions](https://github.com/smart-bug-hunter/smart-bug-hunter/discussions)

---

**Built with ❤️ for the Ethereum security community**
