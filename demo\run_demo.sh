#!/bin/bash

# Smart Bug Hunter Demo Script
# This script demonstrates the complete analysis of a vulnerable Bank contract

set -e  # Exit on any error

echo "🚀 Smart Bug Hunter Demo"
echo "========================"
echo ""

# Check if we're in the right directory
if [ ! -f "Bank.sol" ]; then
    echo "❌ Error: Bank.sol not found. Please run this script from the demo/ directory."
    exit 1
fi

# Check if Foundry is installed
if ! command -v forge &> /dev/null; then
    echo "❌ Error: Foundry not found. Please install Foundry first:"
    echo "   curl -L https://foundry.paradigm.xyz | bash"
    echo "   foundryup"
    exit 1
fi

# Check if smart-bug-hunter is installed
if ! command -v smart-bug-hunter &> /dev/null; then
    echo "❌ Error: smart-bug-hunter not found. Please install it first:"
    echo "   pip install smart-bug-hunter"
    exit 1
fi

# Initialize Foundry project if needed
if [ ! -f "lib/forge-std/src/Test.sol" ]; then
    echo "📦 Initializing Foundry project..."
    forge init --no-git --force .
    echo ""
fi

# Set OpenAI API key if not set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  Warning: OPENAI_API_KEY not set. Invariant verification will be skipped."
    echo "   To enable full analysis, set your OpenAI API key:"
    echo "   export OPENAI_API_KEY='your-api-key-here'"
    echo ""
    
    # Run only fuzzing
    echo "🔍 Running fuzzing analysis only..."
    smart-bug-hunter fuzz Bank.sol --schema schema.json --verbose
else
    # Run complete analysis
    echo "🔍 Running complete analysis (fuzzing + invariant verification)..."
    smart-bug-hunter all Bank.sol --schema schema.json --verbose
fi

echo ""
echo "✅ Demo completed!"
echo ""
echo "📄 Check the generated report:"
echo "   cat output/smart_bug_hunter_report.md"
echo ""
echo "📊 View detailed results:"
echo "   ls -la output/"
echo ""
echo "🔍 Expected findings:"
echo "   - Integer overflow vulnerabilities"
echo "   - Reentrancy issues"
echo "   - Access control problems"
echo "   - Improper balance validation"
echo ""
