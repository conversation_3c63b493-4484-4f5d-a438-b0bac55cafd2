// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_addAdmin_1 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_addAdmin_pool_1() public {
        // Test pool 1: {'newAdmin': '0x1111111111111111111111111111111111111111'}
        try target.addAdmin(address(0x1111111111111111111111111111111111111111)) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}