{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "addAdmin", "inputs": [{"name": "newAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "balances", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "batchDeposit", "inputs": [{"name": "amounts", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "calculateInterest", "inputs": [{"name": "principal", "type": "uint256", "internalType": "uint256"}, {"name": "rate", "type": "uint256", "internalType": "uint256"}, {"name": "time", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "deposit", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "emergencyWithdraw", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getBalance", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getContractBalance", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isAdmin", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "safeWithdraw", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "totalDeposits", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "AdminAdded", "inputs": [{"name": "admin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "399:5796:0:-:0;;;1189:85;;;;;;;;;;1221:10;1213:5;;:18;;;;;;;;;;;;;;;;;;1263:4;1241:7;:19;1249:10;1241:19;;;;;;;;;;;;;;;;:26;;;;;;;;;;;;;;;;;;399:5796;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "399:5796:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5769:253;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;4937:583;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;468:39;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;419:43;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1904:510;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;4055:322;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;4695:107;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3634:313;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;544:28;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;518:20;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3157:343;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2542:455;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1404:374;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;4494:104;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;5769:253;5893:7;6010:5;6002:4;5995;5983:9;:16;;;;:::i;:::-;:23;;;;:::i;:::-;5982:33;;;;:::i;:::-;5975:40;;5769:253;;;;;:::o;4937:583::-;5014:19;5117:9;5112:218;5136:7;;:14;;5132:1;:18;5112:218;;;5192:1;5179:7;;5187:1;5179:10;;;;;;;:::i;:::-;;;;;;;;:14;5171:61;;;;;;;;;;;;:::i;:::-;;;;;;;;;5261:7;;5269:1;5261:10;;;;;;;:::i;:::-;;;;;;;;5246:25;;;;;:::i;:::-;;;5309:7;;5317:1;5309:10;;;;;;;:::i;:::-;;;;;;;;5285:8;:20;5294:10;5285:20;;;;;;;;;;;;;;;;:34;;;;;;;:::i;:::-;;;;;;;;5152:3;;;;;;;5112:218;;;;5369:11;5356:9;:24;5348:71;;;;;;;;;;;;:::i;:::-;;;;;;;;;5446:11;5429:13;;:28;;;;;;;:::i;:::-;;;;;;;;5489:10;5481:32;;;5501:11;5481:32;;;;;;:::i;:::-;;;;;;;;5004:516;4937:583;;:::o;468:39::-;;;;;;;;;;;;;;;;;;;;;;:::o;419:43::-;;;;;;;;;;;;;;;;;:::o;1904:510::-;1974:1;1965:6;:10;1957:52;;;;;;;;;;;;:::i;:::-;;;;;;;;;2051:6;2027:8;:20;2036:10;2027:20;;;;;;;;;;;;;;;;:30;;2019:63;;;;;;;;;;;;:::i;:::-;;;;;;;;;2174:12;2192:10;:15;;2215:6;2192:34;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2173:53;;;2244:7;2236:35;;;;;;;;;;;;:::i;:::-;;;;;;;;;2314:6;2290:8;:20;2299:10;2290:20;;;;;;;;;;;;;;;;:30;;;;;;;:::i;:::-;;;;;;;;2347:6;2330:13;;:23;;;;;;;:::i;:::-;;;;;;;;2388:10;2377:30;;;2400:6;2377:30;;;;;;:::i;:::-;;;;;;;;1947:467;1904:510;:::o;4055:322::-;938:7;:19;946:10;938:19;;;;;;;;;;;;;;;;;;;;;;;;;:42;;;;975:5;;;;;;;;;;;961:19;;:10;:19;;;938:42;930:88;;;;;;;;;;;;:::i;:::-;;;;;;;;;4145:21:::1;4135:6;:31;;4127:73;;;;;;;;;;;;:::i;:::-;;;;;;;;;4273:12;4291:10;:15;;4314:6;4291:34;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4272:53;;;4343:7;4335:35;;;;;;;;;;;;:::i;:::-;;;;;;;;;4117:260;4055:322:::0;:::o;4695:107::-;4748:7;4774:21;4767:28;;4695:107;:::o;3634:313::-;3717:1;3697:22;;:8;:22;;;3689:67;;;;;;;;;;;;:::i;:::-;;;;;;;;;3892:4;3872:7;:17;3880:8;3872:17;;;;;;;;;;;;;;;;:24;;;;;;;;;;;;;;;;;;3931:8;3920:20;;;;;;;;;;;;3634:313;:::o;544:28::-;;;;:::o;518:20::-;;;;;;;;;;;;;:::o;3157:343::-;3244:1;3230:16;;:2;:16;;;3222:60;;;;;;;;;;;;:::i;:::-;;;;;;;;;3309:1;3300:6;:10;3292:52;;;;;;;;;;;;:::i;:::-;;;;;;;;;3455:6;3431:8;:20;3440:10;3431:20;;;;;;;;;;;;;;;;:30;;;;;;;:::i;:::-;;;;;;;;3487:6;3471:8;:12;3480:2;3471:12;;;;;;;;;;;;;;;;:22;;;;;;;:::i;:::-;;;;;;;;3157:343;;:::o;2542:455::-;1089:6;;;;;;;;;;;1088:7;1080:34;;;;;;;;;;;;:::i;:::-;;;;;;;;;1133:4;1124:6;;:13;;;;;;;;;;;;;;;;;;2629:1:::1;2620:6;:10;2612:52;;;;;;;;;;;;:::i;:::-;;;;;;;;;2706:6;2682:8;:20:::0;2691:10:::1;2682:20;;;;;;;;;;;;;;;;:30;;2674:63;;;;;;;;;;;;:::i;:::-;;;;;;;;;2780:6;2756:8;:20:::0;2765:10:::1;2756:20;;;;;;;;;;;;;;;;:30;;;;;;;:::i;:::-;;;;;;;;2813:6;2796:13;;:23;;;;;;;:::i;:::-;;;;;;;;2839:12;2857:10;:15;;2880:6;2857:34;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2838:53;;;2909:7;2901:35;;;;;;;;;;;;:::i;:::-;;;;;;;;;2971:10;2960:30;;;2983:6;2960:30;;;;;;:::i;:::-;;;;;;;;2602:395;1167:5:::0;1158:6;;:14;;;;;;;;;;;;;;;;;;2542:455;:::o;1404:374::-;1481:1;1472:6;:10;1464:52;;;;;;;;;;;;:::i;:::-;;;;;;;;;1547:6;1534:9;:19;1526:60;;;;;;;;;;;;:::i;:::-;;;;;;;;;1681:6;1657:8;:20;1666:10;1657:20;;;;;;;;;;;;;;;;:30;;;;;;;:::i;:::-;;;;;;;;1714:6;1697:13;;:23;;;;;;;:::i;:::-;;;;;;;;1752:10;1744:27;;;1764:6;1744:27;;;;;;:::i;:::-;;;;;;;;1404:374;:::o;4494:104::-;4551:7;4577:8;:14;4586:4;4577:14;;;;;;;;;;;;;;;;4570:21;;4494:104;;;:::o;88:117:49:-;197:1;194;187:12;211:117;320:1;317;310:12;334:77;371:7;400:5;389:16;;334:77;;;:::o;417:122::-;490:24;508:5;490:24;:::i;:::-;483:5;480:35;470:63;;529:1;526;519:12;470:63;417:122;:::o;545:139::-;591:5;629:6;616:20;607:29;;645:33;672:5;645:33;:::i;:::-;545:139;;;;:::o;690:619::-;767:6;775;783;832:2;820:9;811:7;807:23;803:32;800:119;;;838:79;;:::i;:::-;800:119;958:1;983:53;1028:7;1019:6;1008:9;1004:22;983:53;:::i;:::-;973:63;;929:117;1085:2;1111:53;1156:7;1147:6;1136:9;1132:22;1111:53;:::i;:::-;1101:63;;1056:118;1213:2;1239:53;1284:7;1275:6;1264:9;1260:22;1239:53;:::i;:::-;1229:63;;1184:118;690:619;;;;;:::o;1315:118::-;1402:24;1420:5;1402:24;:::i;:::-;1397:3;1390:37;1315:118;;:::o;1439:222::-;1532:4;1570:2;1559:9;1555:18;1547:26;;1583:71;1651:1;1640:9;1636:17;1627:6;1583:71;:::i;:::-;1439:222;;;;:::o;1667:117::-;1776:1;1773;1766:12;1790:117;1899:1;1896;1889:12;1913:117;2022:1;2019;2012:12;2053:568;2126:8;2136:6;2186:3;2179:4;2171:6;2167:17;2163:27;2153:122;;2194:79;;:::i;:::-;2153:122;2307:6;2294:20;2284:30;;2337:18;2329:6;2326:30;2323:117;;;2359:79;;:::i;:::-;2323:117;2473:4;2465:6;2461:17;2449:29;;2527:3;2519:4;2511:6;2507:17;2497:8;2493:32;2490:41;2487:128;;;2534:79;;:::i;:::-;2487:128;2053:568;;;;;:::o;2627:559::-;2713:6;2721;2770:2;2758:9;2749:7;2745:23;2741:32;2738:119;;;2776:79;;:::i;:::-;2738:119;2924:1;2913:9;2909:17;2896:31;2954:18;2946:6;2943:30;2940:117;;;2976:79;;:::i;:::-;2940:117;3089:80;3161:7;3152:6;3141:9;3137:22;3089:80;:::i;:::-;3071:98;;;;2867:312;2627:559;;;;;:::o;3192:126::-;3229:7;3269:42;3262:5;3258:54;3247:65;;3192:126;;;:::o;3324:96::-;3361:7;3390:24;3408:5;3390:24;:::i;:::-;3379:35;;3324:96;;;:::o;3426:122::-;3499:24;3517:5;3499:24;:::i;:::-;3492:5;3489:35;3479:63;;3538:1;3535;3528:12;3479:63;3426:122;:::o;3554:139::-;3600:5;3638:6;3625:20;3616:29;;3654:33;3681:5;3654:33;:::i;:::-;3554:139;;;;:::o;3699:329::-;3758:6;3807:2;3795:9;3786:7;3782:23;3778:32;3775:119;;;3813:79;;:::i;:::-;3775:119;3933:1;3958:53;4003:7;3994:6;3983:9;3979:22;3958:53;:::i;:::-;3948:63;;3904:117;3699:329;;;;:::o;4034:90::-;4068:7;4111:5;4104:13;4097:21;4086:32;;4034:90;;;:::o;4130:109::-;4211:21;4226:5;4211:21;:::i;:::-;4206:3;4199:34;4130:109;;:::o;4245:210::-;4332:4;4370:2;4359:9;4355:18;4347:26;;4383:65;4445:1;4434:9;4430:17;4421:6;4383:65;:::i;:::-;4245:210;;;;:::o;4461:329::-;4520:6;4569:2;4557:9;4548:7;4544:23;4540:32;4537:119;;;4575:79;;:::i;:::-;4537:119;4695:1;4720:53;4765:7;4756:6;4745:9;4741:22;4720:53;:::i;:::-;4710:63;;4666:117;4461:329;;;;:::o;4796:118::-;4883:24;4901:5;4883:24;:::i;:::-;4878:3;4871:37;4796:118;;:::o;4920:222::-;5013:4;5051:2;5040:9;5036:18;5028:26;;5064:71;5132:1;5121:9;5117:17;5108:6;5064:71;:::i;:::-;4920:222;;;;:::o;5148:474::-;5216:6;5224;5273:2;5261:9;5252:7;5248:23;5244:32;5241:119;;;5279:79;;:::i;:::-;5241:119;5399:1;5424:53;5469:7;5460:6;5449:9;5445:22;5424:53;:::i;:::-;5414:63;;5370:117;5526:2;5552:53;5597:7;5588:6;5577:9;5573:22;5552:53;:::i;:::-;5542:63;;5497:118;5148:474;;;;;:::o;5628:180::-;5676:77;5673:1;5666:88;5773:4;5770:1;5763:15;5797:4;5794:1;5787:15;5814:410;5854:7;5877:20;5895:1;5877:20;:::i;:::-;5872:25;;5911:20;5929:1;5911:20;:::i;:::-;5906:25;;5966:1;5963;5959:9;5988:30;6006:11;5988:30;:::i;:::-;5977:41;;6167:1;6158:7;6154:15;6151:1;6148:22;6128:1;6121:9;6101:83;6078:139;;6197:18;;:::i;:::-;6078:139;5862:362;5814:410;;;;:::o;6230:180::-;6278:77;6275:1;6268:88;6375:4;6372:1;6365:15;6399:4;6396:1;6389:15;6416:185;6456:1;6473:20;6491:1;6473:20;:::i;:::-;6468:25;;6507:20;6525:1;6507:20;:::i;:::-;6502:25;;6546:1;6536:35;;6551:18;;:::i;:::-;6536:35;6593:1;6590;6586:9;6581:14;;6416:185;;;;:::o;6607:180::-;6655:77;6652:1;6645:88;6752:4;6749:1;6742:15;6776:4;6773:1;6766:15;6793:169;6877:11;6911:6;6906:3;6899:19;6951:4;6946:3;6942:14;6927:29;;6793:169;;;;:::o;6968:221::-;7108:34;7104:1;7096:6;7092:14;7085:58;7177:4;7172:2;7164:6;7160:15;7153:29;6968:221;:::o;7195:366::-;7337:3;7358:67;7422:2;7417:3;7358:67;:::i;:::-;7351:74;;7434:93;7523:3;7434:93;:::i;:::-;7552:2;7547:3;7543:12;7536:19;;7195:366;;;:::o;7567:419::-;7733:4;7771:2;7760:9;7756:18;7748:26;;7820:9;7814:4;7810:20;7806:1;7795:9;7791:17;7784:47;7848:131;7974:4;7848:131;:::i;:::-;7840:139;;7567:419;;;:::o;7992:191::-;8032:3;8051:20;8069:1;8051:20;:::i;:::-;8046:25;;8085:20;8103:1;8085:20;:::i;:::-;8080:25;;8128:1;8125;8121:9;8114:16;;8149:3;8146:1;8143:10;8140:36;;;8156:18;;:::i;:::-;8140:36;7992:191;;;;:::o;8189:221::-;8329:34;8325:1;8317:6;8313:14;8306:58;8398:4;8393:2;8385:6;8381:15;8374:29;8189:221;:::o;8416:366::-;8558:3;8579:67;8643:2;8638:3;8579:67;:::i;:::-;8572:74;;8655:93;8744:3;8655:93;:::i;:::-;8773:2;8768:3;8764:12;8757:19;;8416:366;;;:::o;8788:419::-;8954:4;8992:2;8981:9;8977:18;8969:26;;9041:9;9035:4;9031:20;9027:1;9016:9;9012:17;9005:47;9069:131;9195:4;9069:131;:::i;:::-;9061:139;;8788:419;;;:::o;9213:179::-;9353:31;9349:1;9341:6;9337:14;9330:55;9213:179;:::o;9398:366::-;9540:3;9561:67;9625:2;9620:3;9561:67;:::i;:::-;9554:74;;9637:93;9726:3;9637:93;:::i;:::-;9755:2;9750:3;9746:12;9739:19;;9398:366;;;:::o;9770:419::-;9936:4;9974:2;9963:9;9959:18;9951:26;;10023:9;10017:4;10013:20;10009:1;9998:9;9994:17;9987:47;10051:131;10177:4;10051:131;:::i;:::-;10043:139;;9770:419;;;:::o;10195:170::-;10335:22;10331:1;10323:6;10319:14;10312:46;10195:170;:::o;10371:366::-;10513:3;10534:67;10598:2;10593:3;10534:67;:::i;:::-;10527:74;;10610:93;10699:3;10610:93;:::i;:::-;10728:2;10723:3;10719:12;10712:19;;10371:366;;;:::o;10743:419::-;10909:4;10947:2;10936:9;10932:18;10924:26;;10996:9;10990:4;10986:20;10982:1;10971:9;10967:17;10960:47;11024:131;11150:4;11024:131;:::i;:::-;11016:139;;10743:419;;;:::o;11168:147::-;11269:11;11306:3;11291:18;;11168:147;;;;:::o;11321:114::-;;:::o;11441:398::-;11600:3;11621:83;11702:1;11697:3;11621:83;:::i;:::-;11614:90;;11713:93;11802:3;11713:93;:::i;:::-;11831:1;11826:3;11822:11;11815:18;;11441:398;;;:::o;11845:379::-;12029:3;12051:147;12194:3;12051:147;:::i;:::-;12044:154;;12215:3;12208:10;;11845:379;;;:::o;12230:165::-;12370:17;12366:1;12358:6;12354:14;12347:41;12230:165;:::o;12401:366::-;12543:3;12564:67;12628:2;12623:3;12564:67;:::i;:::-;12557:74;;12640:93;12729:3;12640:93;:::i;:::-;12758:2;12753:3;12749:12;12742:19;;12401:366;;;:::o;12773:419::-;12939:4;12977:2;12966:9;12962:18;12954:26;;13026:9;13020:4;13016:20;13012:1;13001:9;12997:17;12990:47;13054:131;13180:4;13054:131;:::i;:::-;13046:139;;12773:419;;;:::o;13198:194::-;13238:4;13258:20;13276:1;13258:20;:::i;:::-;13253:25;;13292:20;13310:1;13292:20;:::i;:::-;13287:25;;13336:1;13333;13329:9;13321:17;;13360:1;13354:4;13351:11;13348:37;;;13365:18;;:::i;:::-;13348:37;13198:194;;;;:::o;13398:220::-;13538:34;13534:1;13526:6;13522:14;13515:58;13607:3;13602:2;13594:6;13590:15;13583:28;13398:220;:::o;13624:366::-;13766:3;13787:67;13851:2;13846:3;13787:67;:::i;:::-;13780:74;;13863:93;13952:3;13863:93;:::i;:::-;13981:2;13976:3;13972:12;13965:19;;13624:366;;;:::o;13996:419::-;14162:4;14200:2;14189:9;14185:18;14177:26;;14249:9;14243:4;14239:20;14235:1;14224:9;14220:17;14213:47;14277:131;14403:4;14277:131;:::i;:::-;14269:139;;13996:419;;;:::o;14421:179::-;14561:31;14557:1;14549:6;14545:14;14538:55;14421:179;:::o;14606:366::-;14748:3;14769:67;14833:2;14828:3;14769:67;:::i;:::-;14762:74;;14845:93;14934:3;14845:93;:::i;:::-;14963:2;14958:3;14954:12;14947:19;;14606:366;;;:::o;14978:419::-;15144:4;15182:2;15171:9;15167:18;15159:26;;15231:9;15225:4;15221:20;15217:1;15206:9;15202:17;15195:47;15259:131;15385:4;15259:131;:::i;:::-;15251:139;;14978:419;;;:::o;15403:182::-;15543:34;15539:1;15531:6;15527:14;15520:58;15403:182;:::o;15591:366::-;15733:3;15754:67;15818:2;15813:3;15754:67;:::i;:::-;15747:74;;15830:93;15919:3;15830:93;:::i;:::-;15948:2;15943:3;15939:12;15932:19;;15591:366;;;:::o;15963:419::-;16129:4;16167:2;16156:9;16152:18;16144:26;;16216:9;16210:4;16206:20;16202:1;16191:9;16187:17;16180:47;16244:131;16370:4;16244:131;:::i;:::-;16236:139;;15963:419;;;:::o;16388:181::-;16528:33;16524:1;16516:6;16512:14;16505:57;16388:181;:::o;16575:366::-;16717:3;16738:67;16802:2;16797:3;16738:67;:::i;:::-;16731:74;;16814:93;16903:3;16814:93;:::i;:::-;16932:2;16927:3;16923:12;16916:19;;16575:366;;;:::o;16947:419::-;17113:4;17151:2;17140:9;17136:18;17128:26;;17200:9;17194:4;17190:20;17186:1;17175:9;17171:17;17164:47;17228:131;17354:4;17228:131;:::i;:::-;17220:139;;16947:419;;;:::o;17372:164::-;17512:16;17508:1;17500:6;17496:14;17489:40;17372:164;:::o;17542:366::-;17684:3;17705:67;17769:2;17764:3;17705:67;:::i;:::-;17698:74;;17781:93;17870:3;17781:93;:::i;:::-;17899:2;17894:3;17890:12;17883:19;;17542:366;;;:::o;17914:419::-;18080:4;18118:2;18107:9;18103:18;18095:26;;18167:9;18161:4;18157:20;18153:1;18142:9;18138:17;18131:47;18195:131;18321:4;18195:131;:::i;:::-;18187:139;;17914:419;;;:::o;18339:178::-;18479:30;18475:1;18467:6;18463:14;18456:54;18339:178;:::o;18523:366::-;18665:3;18686:67;18750:2;18745:3;18686:67;:::i;:::-;18679:74;;18762:93;18851:3;18762:93;:::i;:::-;18880:2;18875:3;18871:12;18864:19;;18523:366;;;:::o;18895:419::-;19061:4;19099:2;19088:9;19084:18;19076:26;;19148:9;19142:4;19138:20;19134:1;19123:9;19119:17;19112:47;19176:131;19302:4;19176:131;:::i;:::-;19168:139;;18895:419;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"addAdmin(address)": "70480275", "balances(address)": "27e235e3", "batchDeposit(uint256[])": "1e51d569", "calculateInterest(uint256,uint256,uint256)": "05e1bd8c", "deposit(uint256)": "b6b55f25", "emergencyWithdraw(uint256)": "5312ea8e", "getBalance(address)": "f8b2cb4f", "getContractBalance()": "6f9fb98a", "isAdmin(address)": "24d7806c", "owner()": "8da5cb5b", "safeWithdraw(uint256)": "b0fd035b", "totalDeposits()": "7d882097", "transfer(address,uint256)": "a9059cbb", "withdraw(uint256)": "2e1a7d4d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"}],\"name\":\"AdminAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Withdrawal\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newAdmin\",\"type\":\"address\"}],\"name\":\"addAdmin\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"balances\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"}],\"name\":\"batchDeposit\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"principal\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"rate\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"time\",\"type\":\"uint256\"}],\"name\":\"calculateInterest\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"emergencyWithdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"getBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getContractBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"isAdmin\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"safeWithdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalDeposits\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"details\":\"This contract contains several intentional vulnerabilities for demonstration purposes  Vulnerabilities included: 1. Integer overflow in deposit function 2. Reentrancy vulnerability in withdraw function 3. Access control issues in admin functions 4. Improper balance checks\",\"kind\":\"dev\",\"methods\":{\"addAdmin(address)\":{\"details\":\"Admin function with access control vulnerability\",\"params\":{\"newAdmin\":\"Address to add as admin\"}},\"batchDeposit(uint256[])\":{\"details\":\"Batch deposit function with loop vulnerability\",\"params\":{\"amounts\":\"Array of amounts to deposit\"}},\"calculateInterest(uint256,uint256,uint256)\":{\"details\":\"Calculate interest with precision issues\",\"params\":{\"principal\":\"Principal amount\",\"rate\":\"Interest rate (in basis points, e.g., 500 = 5%)\",\"time\":\"Time period\"},\"returns\":{\"_0\":\"Interest amount\"}},\"deposit(uint256)\":{\"details\":\"Deposit function with integer overflow vulnerability\",\"params\":{\"amount\":\"Amount to deposit\"}},\"emergencyWithdraw(uint256)\":{\"details\":\"Emergency withdraw for admins\",\"params\":{\"amount\":\"Amount to withdraw\"}},\"getBalance(address)\":{\"details\":\"Get user balance\",\"params\":{\"user\":\"User address\"},\"returns\":{\"_0\":\"User's balance\"}},\"getContractBalance()\":{\"details\":\"Get contract balance\",\"returns\":{\"_0\":\"Contract's ETH balance\"}},\"safeWithdraw(uint256)\":{\"details\":\"Safe withdraw function with reentrancy protection\",\"params\":{\"amount\":\"Amount to withdraw\"}},\"transfer(address,uint256)\":{\"details\":\"Transfer function with insufficient validation\",\"params\":{\"amount\":\"Amount to transfer\",\"to\":\"Recipient address\"}},\"withdraw(uint256)\":{\"details\":\"Withdraw function with reentrancy vulnerability\",\"params\":{\"amount\":\"Amount to withdraw\"}}},\"title\":\"Vulnerable Bank Contract\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"Bank.sol\":\"Bank\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"Bank.sol\":{\"keccak256\":\"0xd8bc6b4029c43f2d1afe25ddb89d0ebe824c55ddedb761266227224935941b6a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8f6e0ea463cf8dfebe815d54d4f16d2fdd41cb3b027d92993cb45208e27e3ffb\",\"dweb:/ipfs/QmZRAYEg669cMRuTX4bXB6faLv8EXsn9LhZydSMkE9EPpw\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address", "indexed": true}], "type": "event", "name": "AdminAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newAdmin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "addAdmin"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "stateMutability": "payable", "type": "function", "name": "batchDeposit"}, {"inputs": [{"internalType": "uint256", "name": "principal", "type": "uint256"}, {"internalType": "uint256", "name": "rate", "type": "uint256"}, {"internalType": "uint256", "name": "time", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "calculateInterest", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "deposit"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "emergencyWithdraw"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getContractBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "safeWithdraw"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalDeposits", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"addAdmin(address)": {"details": "Admin function with access control vulnerability", "params": {"newAdmin": "Address to add as admin"}}, "batchDeposit(uint256[])": {"details": "Batch deposit function with loop vulnerability", "params": {"amounts": "Array of amounts to deposit"}}, "calculateInterest(uint256,uint256,uint256)": {"details": "Calculate interest with precision issues", "params": {"principal": "Principal amount", "rate": "Interest rate (in basis points, e.g., 500 = 5%)", "time": "Time period"}, "returns": {"_0": "Interest amount"}}, "deposit(uint256)": {"details": "Deposit function with integer overflow vulnerability", "params": {"amount": "Amount to deposit"}}, "emergencyWithdraw(uint256)": {"details": "Emergency withdraw for admins", "params": {"amount": "Amount to withdraw"}}, "getBalance(address)": {"details": "Get user balance", "params": {"user": "User address"}, "returns": {"_0": "User's balance"}}, "getContractBalance()": {"details": "Get contract balance", "returns": {"_0": "Contract's ETH balance"}}, "safeWithdraw(uint256)": {"details": "Safe withdraw function with reentrancy protection", "params": {"amount": "Amount to withdraw"}}, "transfer(address,uint256)": {"details": "Transfer function with insufficient validation", "params": {"amount": "Amount to transfer", "to": "Recipient address"}}, "withdraw(uint256)": {"details": "Withdraw function with reentrancy vulnerability", "params": {"amount": "Amount to withdraw"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"Bank.sol": "Bank"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"Bank.sol": {"keccak256": "0xd8bc6b4029c43f2d1afe25ddb89d0ebe824c55ddedb761266227224935941b6a", "urls": ["bzz-raw://8f6e0ea463cf8dfebe815d54d4f16d2fdd41cb3b027d92993cb45208e27e3ffb", "dweb:/ipfs/QmZRAYEg669cMRuTX4bXB6faLv8EXsn9LhZydSMkE9EPpw"], "license": "MIT"}}, "version": 1}, "id": 0}