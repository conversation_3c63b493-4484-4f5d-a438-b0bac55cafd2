// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_calculateInterest_13 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_calculateInterest_pool_13() public {
        // Test pool 13: {'principal': 1000, 'rate': 500, 'time': 365}
        try target.calculateInterest(1000, 500, 365) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}