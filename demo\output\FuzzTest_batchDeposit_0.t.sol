// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_batchDeposit_0 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_batchDeposit_pool_0() public {
        // Test pool 0: {'amounts': [1, 2, 3]}
        try target.batchDeposit([1, 2, 3]) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}