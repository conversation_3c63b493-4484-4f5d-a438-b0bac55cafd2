"""
Data models for smart-bug-hunter.

Defines the core data structures used throughout the application.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from enum import Enum


class SolverType(str, Enum):
    """Supported SMT solvers."""
    Z3 = "z3"
    CVC5 = "cvc5"


class TestResult(str, Enum):
    """Test execution results."""
    PASS = "pass"
    FAIL = "fail"
    ERROR = "error"
    TIMEOUT = "timeout"


@dataclass
class FunctionSignature:
    """Represents a Solidity function signature."""
    name: str
    inputs: List[Dict[str, str]]  # [{"name": "amount", "type": "uint256"}, ...]
    outputs: List[Dict[str, str]]
    visibility: str  # "public", "external"
    stateMutability: str  # "view", "pure", "payable", "nonpayable"
    
    @property
    def signature(self) -> str:
        """Generate function signature string."""
        input_types = [param["type"] for param in self.inputs]
        return f"{self.name}({','.join(input_types)})"


@dataclass
class TestPool:
    """Represents a combinatorial test pool."""
    id: int
    parameters: List[Any]
    parameter_names: List[str]
    result: TestResult = TestResult.PASS
    error_message: Optional[str] = None
    execution_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "parameters": self.parameters,
            "parameter_names": self.parameter_names,
            "result": self.result.value,
            "error_message": self.error_message,
            "execution_time": self.execution_time,
        }


@dataclass
class FuzzResults:
    """Results from combinatorial fuzzing."""
    contract_path: Path
    total_pools: int
    passed_pools: List[TestPool] = field(default_factory=list)
    failed_pools: List[TestPool] = field(default_factory=list)
    error_pools: List[TestPool] = field(default_factory=list)
    execution_time: float = 0.0
    
    @property
    def has_failures(self) -> bool:
        """Check if any tests failed."""
        return len(self.failed_pools) > 0 or len(self.error_pools) > 0
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_pools == 0:
            return 1.0
        return len(self.passed_pools) / self.total_pools
    
    def get_failing_parameters(self) -> List[Dict[str, Any]]:
        """Extract parameters from failing pools for analysis."""
        failing_params = []
        for pool in self.failed_pools + self.error_pools:
            failing_params.append({
                "pool_id": pool.id,
                "parameters": dict(zip(pool.parameter_names, pool.parameters)),
                "error": pool.error_message,
            })
        return failing_params


@dataclass
class Invariant:
    """Represents a generated invariant."""
    id: str
    function_name: str
    type: str  # "precondition" or "postcondition"
    description: str
    solidity_assertion: str
    smt_formula: Optional[str] = None
    is_verified: Optional[bool] = None
    counterexample: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "function_name": self.function_name,
            "type": self.type,
            "description": self.description,
            "solidity_assertion": self.solidity_assertion,
            "smt_formula": self.smt_formula,
            "is_verified": self.is_verified,
            "counterexample": self.counterexample,
        }


@dataclass
class InvariantResults:
    """Results from invariant verification."""
    contract_path: Path
    total_invariants: int
    verified_invariants: List[Invariant] = field(default_factory=list)
    violations: List[Invariant] = field(default_factory=list)
    errors: List[Invariant] = field(default_factory=list)
    execution_time: float = 0.0
    
    @property
    def has_violations(self) -> bool:
        """Check if any invariants were violated."""
        return len(self.violations) > 0
    
    @property
    def verification_rate(self) -> float:
        """Calculate verification success rate."""
        if self.total_invariants == 0:
            return 1.0
        return len(self.verified_invariants) / self.total_invariants


@dataclass
class ContractAnalysis:
    """Results from contract analysis."""
    contract_path: Path
    contract_name: str
    functions: List[FunctionSignature] = field(default_factory=list)
    compilation_errors: List[str] = field(default_factory=list)
    analysis_errors: List[str] = field(default_factory=list)
    
    @property
    def is_valid(self) -> bool:
        """Check if contract analysis was successful."""
        return len(self.compilation_errors) == 0 and len(self.analysis_errors) == 0
    
    def get_fuzzable_functions(self) -> List[FunctionSignature]:
        """Get functions suitable for fuzzing (public/external with parameters)."""
        return [
            func for func in self.functions
            if func.visibility in ["public", "external"] and len(func.inputs) > 0
        ]
