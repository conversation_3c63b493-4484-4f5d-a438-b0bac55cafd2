"""
Tests for the CLI module.
"""

import pytest
from pathlib import Path
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from smart_bug_hunter.cli import app

runner = CliRunner()


def test_version_command():
    """Test the version command."""
    result = runner.invoke(app, ["version"])
    assert result.exit_code == 0
    assert "smart-bug-hunter version" in result.stdout


def test_fuzz_command_missing_contract():
    """Test fuzz command with missing contract file."""
    result = runner.invoke(app, ["fuzz", "nonexistent.sol"])
    assert result.exit_code == 1
    assert "Contract path not found" in result.stdout


def test_prove_command_missing_contract():
    """Test prove command with missing contract file."""
    result = runner.invoke(app, ["prove", "nonexistent.sol"])
    assert result.exit_code == 1
    assert "Contract path not found" in result.stdout


def test_all_command_missing_contract():
    """Test all command with missing contract file."""
    result = runner.invoke(app, ["all", "nonexistent.sol"])
    assert result.exit_code == 1
    assert "Contract path not found" in result.stdout


def test_help_command():
    """Test help command."""
    result = runner.invoke(app, ["--help"])
    assert result.exit_code == 0
    assert "smart-bug-hunter" in result.stdout
    assert "Combinatorial fuzzing and formal verification" in result.stdout


def test_fuzz_help():
    """Test fuzz command help."""
    result = runner.invoke(app, ["fuzz", "--help"])
    assert result.exit_code == 0
    assert "Run combinatorial fuzzing" in result.stdout


def test_prove_help():
    """Test prove command help."""
    result = runner.invoke(app, ["prove", "--help"])
    assert result.exit_code == 0
    assert "Generate and verify invariants" in result.stdout


def test_all_help():
    """Test all command help."""
    result = runner.invoke(app, ["all", "--help"])
    assert result.exit_code == 0
    assert "Run complete analysis" in result.stdout
