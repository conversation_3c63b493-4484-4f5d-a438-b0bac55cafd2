{"abi": [{"type": "function", "name": "exposed_assumeNotBlacklisted", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "addr", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "exposed_assumeNotPayable", "inputs": [{"name": "addr", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "exposed_assumePayable", "inputs": [{"name": "addr", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "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", "sourceMap": "16683:454:32:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "16683:454:32:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;16821:96;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;16997:138;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;16725:90;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;16821:96;16888:22;16905:4;16888:16;:22::i;:::-;16821:96;:::o;16997:138::-;17095:33;17116:5;17123:4;17095:20;:33::i;:::-;16997:138;;:::o;16725:90::-;16789:19;16803:4;16789:13;:19::i;:::-;16725:90;:::o;9558:102:5:-;318:28;310:37;;9625:9;;;9636:16;9647:4;9636:10;:16::i;:::-;9635:17;9625:28;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9558:102;:::o;5292:903::-;5441:21;5524:5;5512:18;5495:35;;5573:1;5557:13;:17;5549:111;;;;;;;;;;;;:::i;:::-;;;;;;;;;5671:12;5693:23;5822:5;:16;;5862:10;5874:4;5839:40;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5822:58;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5798:82;;;;;;;;318:28;310:37;;5890:9;;;5901:7;5900:8;:51;;;;5946:5;5912:39;;5923:10;5912:30;;;;;;;;;;;;:::i;:::-;:39;;;5900:51;5890:62;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6058:5;:16;;6098:10;6110:4;6075:40;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6058:58;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6034:82;;;;;;;;318:28;310:37;;6126:9;;;6137:7;6136:8;:51;;;;6182:5;6148:39;;6159:10;6148:30;;;;;;;;;;;;:::i;:::-;:39;;;6136:51;6126:62;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5373:822;;;5292:903;;:::o;9454:98::-;318:28;310:37;;9518:9;;;9528:16;9539:4;9528:10;:16::i;:::-;9518:27;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9454:98;:::o;8611:592::-;8662:4;404:78;8699:4;:12;;;:26;8678:167;;;;;;;;;;;;:::i;:::-;;;;;;;;;8855:23;8881:21;8855:47;;8912:23;8946:4;8938:21;;;8912:47;;318:28;310:37;;8970:7;;;8986:4;8993:1;8970:25;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9006:12;9031:4;9023:18;;9049:1;9023:32;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9005:50;;;318:28;310:37;;9092:7;;;9108:4;9115:15;9092:39;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;318:28;310:37;;9141:7;;;9149:4;9155:15;9141:30;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9189:7;9182:14;;;;;8611:592;;;:::o;88:117:49:-;197:1;194;187:12;334:126;371:7;411:42;404:5;400:54;389:65;;334:126;;;:::o;466:96::-;503:7;532:24;550:5;532:24;:::i;:::-;521:35;;466:96;;;:::o;568:122::-;641:24;659:5;641:24;:::i;:::-;634:5;631:35;621:63;;680:1;677;670:12;621:63;568:122;:::o;696:139::-;742:5;780:6;767:20;758:29;;796:33;823:5;796:33;:::i;:::-;696:139;;;;:::o;841:329::-;900:6;949:2;937:9;928:7;924:23;920:32;917:119;;;955:79;;:::i;:::-;917:119;1075:1;1100:53;1145:7;1136:6;1125:9;1121:22;1100:53;:::i;:::-;1090:63;;1046:117;841:329;;;;:::o;1176:474::-;1244:6;1252;1301:2;1289:9;1280:7;1276:23;1272:32;1269:119;;;1307:79;;:::i;:::-;1269:119;1427:1;1452:53;1497:7;1488:6;1477:9;1473:22;1452:53;:::i;:::-;1442:63;;1398:117;1554:2;1580:53;1625:7;1616:6;1605:9;1601:22;1580:53;:::i;:::-;1570:63;;1525:118;1176:474;;;;;:::o;1656:90::-;1690:7;1733:5;1726:13;1719:21;1708:32;;1656:90;;;:::o;1752:109::-;1833:21;1848:5;1833:21;:::i;:::-;1828:3;1821:34;1752:109;;:::o;1867:210::-;1954:4;1992:2;1981:9;1977:18;1969:26;;2005:65;2067:1;2056:9;2052:17;2043:6;2005:65;:::i;:::-;1867:210;;;;:::o;2083:169::-;2167:11;2201:6;2196:3;2189:19;2241:4;2236:3;2232:14;2217:29;;2083:169;;;;:::o;2258:305::-;2398:34;2394:1;2386:6;2382:14;2375:58;2467:34;2462:2;2454:6;2450:15;2443:59;2536:19;2531:2;2523:6;2519:15;2512:44;2258:305;:::o;2569:366::-;2711:3;2732:67;2796:2;2791:3;2732:67;:::i;:::-;2725:74;;2808:93;2897:3;2808:93;:::i;:::-;2926:2;2921:3;2917:12;2910:19;;2569:366;;;:::o;2941:419::-;3107:4;3145:2;3134:9;3130:18;3122:26;;3194:9;3188:4;3184:20;3180:1;3169:9;3165:17;3158:47;3222:131;3348:4;3222:131;:::i;:::-;3214:139;;2941:419;;;:::o;3366:118::-;3453:24;3471:5;3453:24;:::i;:::-;3448:3;3441:37;3366:118;;:::o;3490:222::-;3583:4;3621:2;3610:9;3606:18;3598:26;;3634:71;3702:1;3691:9;3687:17;3678:6;3634:71;:::i;:::-;3490:222;;;;:::o;3718:98::-;3769:6;3803:5;3797:12;3787:22;;3718:98;;;:::o;3822:147::-;3923:11;3960:3;3945:18;;3822:147;;;;:::o;3975:246::-;4056:1;4066:113;4080:6;4077:1;4074:13;4066:113;;;4165:1;4160:3;4156:11;4150:18;4146:1;4141:3;4137:11;4130:39;4102:2;4099:1;4095:10;4090:15;;4066:113;;;4213:1;4204:6;4199:3;4195:16;4188:27;4037:184;3975:246;;;:::o;4227:386::-;4331:3;4359:38;4391:5;4359:38;:::i;:::-;4413:88;4494:6;4489:3;4413:88;:::i;:::-;4406:95;;4510:65;4568:6;4563:3;4556:4;4549:5;4545:16;4510:65;:::i;:::-;4600:6;4595:3;4591:16;4584:23;;4335:278;4227:386;;;;:::o;4619:271::-;4749:3;4771:93;4860:3;4851:6;4771:93;:::i;:::-;4764:100;;4881:3;4874:10;;4619:271;;;;:::o;4896:116::-;4966:21;4981:5;4966:21;:::i;:::-;4959:5;4956:32;4946:60;;5002:1;4999;4992:12;4946:60;4896:116;:::o;5018:137::-;5072:5;5103:6;5097:13;5088:22;;5119:30;5143:5;5119:30;:::i;:::-;5018:137;;;;:::o;5161:345::-;5228:6;5277:2;5265:9;5256:7;5252:23;5248:32;5245:119;;;5283:79;;:::i;:::-;5245:119;5403:1;5428:61;5481:7;5472:6;5461:9;5457:22;5428:61;:::i;:::-;5418:71;;5374:125;5161:345;;;;:::o;5512:318::-;5652:34;5648:1;5640:6;5636:14;5629:58;5721:34;5716:2;5708:6;5704:15;5697:59;5790:32;5785:2;5777:6;5773:15;5766:57;5512:318;:::o;5836:366::-;5978:3;5999:67;6063:2;6058:3;5999:67;:::i;:::-;5992:74;;6075:93;6164:3;6075:93;:::i;:::-;6193:2;6188:3;6184:12;6177:19;;5836:366;;;:::o;6208:419::-;6374:4;6412:2;6401:9;6397:18;6389:26;;6461:9;6455:4;6451:20;6447:1;6436:9;6432:17;6425:47;6489:131;6615:4;6489:131;:::i;:::-;6481:139;;6208:419;;;:::o;6633:85::-;6678:7;6707:5;6696:16;;6633:85;;;:::o;6724:77::-;6761:7;6790:5;6779:16;;6724:77;;;:::o;6807:60::-;6835:3;6856:5;6849:12;;6807:60;;;:::o;6873:158::-;6931:9;6964:61;6982:42;6991:32;7017:5;6991:32;:::i;:::-;6982:42;:::i;:::-;6964:61;:::i;:::-;6951:74;;6873:158;;;:::o;7037:147::-;7132:45;7171:5;7132:45;:::i;:::-;7127:3;7120:58;7037:147;;:::o;7190:348::-;7319:4;7357:2;7346:9;7342:18;7334:26;;7370:71;7438:1;7427:9;7423:17;7414:6;7370:71;:::i;:::-;7451:80;7527:2;7516:9;7512:18;7503:6;7451:80;:::i;:::-;7190:348;;;;;:::o;7544:114::-;;:::o;7664:398::-;7823:3;7844:83;7925:1;7920:3;7844:83;:::i;:::-;7837:90;;7936:93;8025:3;7936:93;:::i;:::-;8054:1;8049:3;8045:11;8038:18;;7664:398;;;:::o;8068:379::-;8252:3;8274:147;8417:3;8274:147;:::i;:::-;8267:154;;8438:3;8431:10;;8068:379;;;:::o;8453:118::-;8540:24;8558:5;8540:24;:::i;:::-;8535:3;8528:37;8453:118;;:::o;8577:332::-;8698:4;8736:2;8725:9;8721:18;8713:26;;8749:71;8817:1;8806:9;8802:17;8793:6;8749:71;:::i;:::-;8830:72;8898:2;8887:9;8883:18;8874:6;8830:72;:::i;:::-;8577:332;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"exposed_assumeNotBlacklisted(address,address)": "95b9a383", "exposed_assumeNotPayable(address)": "17cc73a0", "exposed_assumePayable(address)": "de4a5dcd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"}],\"name\":\"exposed_assumeNotBlacklisted\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"}],\"name\":\"exposed_assumeNotPayable\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"}],\"name\":\"exposed_assumePayable\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/test/StdCheats.t.sol\":\"StdCheatsMock\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x399d0b11c8e4e902b6c95c21c187968e62e1bd2a86b8f6dad882c399a05404c1\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://82618dd9135c30870716bb9561f238b68b0c46cff6c7311a3d4b1c35a541c633\",\"dweb:/ipfs/QmSw3gEwXthgoDAqE3gemC5JfQnBnRBTFGEJaDNBj7JwxH\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IERC20.sol\":{\"keccak256\":\"0x4cab887298790f908c27de107e4e2907ca5413aee482ef776f8d2f353c5ef947\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bb715e0c4a2bdbe432bb624501506041f06e878e0b72675aebba30ad2c2b72e7\",\"dweb:/ipfs/QmWhhLSvkxS2NrukJJHqFY8gDVE5r9rD4PfHvR24pwdKv9\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/forge-std/test/StdCheats.t.sol\":{\"keccak256\":\"0xd85a115be2a96e2da3f2b0be38b69685c288e8d3b49e333404a30a2d348615ee\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c8821aae501f053c679b75bf09c87ad643caa51ca3b9c4355b67d3c768a643ac\",\"dweb:/ipfs/QmXKQoxhd9vivNF5QgredzhZqhTVeWxuWH1MdtuawqL3s9\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "addr", "type": "address"}], "stateMutability": "view", "type": "function", "name": "exposed_assumeNotBlacklisted"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "exposed_assumeNotPayable"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "exposed_assumePayable"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/test/StdCheats.t.sol": "StdCheatsMock"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x399d0b11c8e4e902b6c95c21c187968e62e1bd2a86b8f6dad882c399a05404c1", "urls": ["bzz-raw://82618dd9135c30870716bb9561f238b68b0c46cff6c7311a3d4b1c35a541c633", "dweb:/ipfs/QmSw3gEwXthgoDAqE3gemC5JfQnBnRBTFGEJaDNBj7JwxH"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IERC20.sol": {"keccak256": "0x4cab887298790f908c27de107e4e2907ca5413aee482ef776f8d2f353c5ef947", "urls": ["bzz-raw://bb715e0c4a2bdbe432bb624501506041f06e878e0b72675aebba30ad2c2b72e7", "dweb:/ipfs/QmWhhLSvkxS2NrukJJHqFY8gDVE5r9rD4PfHvR24pwdKv9"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/forge-std/test/StdCheats.t.sol": {"keccak256": "0xd85a115be2a96e2da3f2b0be38b69685c288e8d3b49e333404a30a2d348615ee", "urls": ["bzz-raw://c8821aae501f053c679b75bf09c87ad643caa51ca3b9c4355b67d3c768a643ac", "dweb:/ipfs/QmXKQoxhd9vivNF5QgredzhZqhTVeWxuWH1MdtuawqL3s9"], "license": "MIT"}}, "version": 1}, "id": 32}