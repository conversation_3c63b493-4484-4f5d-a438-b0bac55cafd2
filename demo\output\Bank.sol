// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title Vulnerable Bank Contract
 * @dev This contract contains several intentional vulnerabilities for demonstration purposes
 * 
 * Vulnerabilities included:
 * 1. Integer overflow in deposit function
 * 2. Reentrancy vulnerability in withdraw function
 * 3. Access control issues in admin functions
 * 4. Improper balance checks
 */
contract Bank {
    mapping(address => uint256) public balances;
    mapping(address => bool) public isAdmin;
    
    address public owner;
    uint256 public totalDeposits;
    bool private locked;
    
    event Deposit(address indexed user, uint256 amount);
    event Withdrawal(address indexed user, uint256 amount);
    event AdminAdded(address indexed admin);
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
    
    modifier onlyAdmin() {
        require(isAdmin[msg.sender] || msg.sender == owner, "Only admin can call this function");
        _;
    }
    
    modifier noReentrancy() {
        require(!locked, "Reentrant call");
        locked = true;
        _;
        locked = false;
    }
    
    constructor() {
        owner = msg.sender;
        isAdmin[msg.sender] = true;
    }
    
    /**
     * @dev Deposit function with integer overflow vulnerability
     * @param amount Amount to deposit
     */
    function deposit(uint256 amount) external payable {
        require(amount > 0, "Amount must be greater than 0");
        require(msg.value == amount, "Sent value must match amount");
        
        // VULNERABILITY: Integer overflow possible
        balances[msg.sender] += amount;
        totalDeposits += amount;
        
        emit Deposit(msg.sender, amount);
    }
    
    /**
     * @dev Withdraw function with reentrancy vulnerability
     * @param amount Amount to withdraw
     */
    function withdraw(uint256 amount) external {
        require(amount > 0, "Amount must be greater than 0");
        require(balances[msg.sender] >= amount, "Insufficient balance");
        
        // VULNERABILITY: State change after external call (reentrancy)
        (bool success, ) = msg.sender.call{value: amount}("");
        require(success, "Transfer failed");
        
        balances[msg.sender] -= amount;
        totalDeposits -= amount;
        
        emit Withdrawal(msg.sender, amount);
    }
    
    /**
     * @dev Safe withdraw function with reentrancy protection
     * @param amount Amount to withdraw
     */
    function safeWithdraw(uint256 amount) external noReentrancy {
        require(amount > 0, "Amount must be greater than 0");
        require(balances[msg.sender] >= amount, "Insufficient balance");
        
        balances[msg.sender] -= amount;
        totalDeposits -= amount;
        
        (bool success, ) = msg.sender.call{value: amount}("");
        require(success, "Transfer failed");
        
        emit Withdrawal(msg.sender, amount);
    }
    
    /**
     * @dev Transfer function with insufficient validation
     * @param to Recipient address
     * @param amount Amount to transfer
     */
    function transfer(address to, uint256 amount) external {
        require(to != address(0), "Cannot transfer to zero address");
        require(amount > 0, "Amount must be greater than 0");
        
        // VULNERABILITY: No check if sender has sufficient balance
        balances[msg.sender] -= amount;
        balances[to] += amount;
    }
    
    /**
     * @dev Admin function with access control vulnerability
     * @param newAdmin Address to add as admin
     */
    function addAdmin(address newAdmin) external {
        require(newAdmin != address(0), "Cannot add zero address as admin");
        
        // VULNERABILITY: Missing proper access control
        // Anyone can call this function
        isAdmin[newAdmin] = true;
        
        emit AdminAdded(newAdmin);
    }
    
    /**
     * @dev Emergency withdraw for admins
     * @param amount Amount to withdraw
     */
    function emergencyWithdraw(uint256 amount) external onlyAdmin {
        require(amount <= address(this).balance, "Insufficient contract balance");
        
        // VULNERABILITY: No balance tracking update
        (bool success, ) = msg.sender.call{value: amount}("");
        require(success, "Transfer failed");
    }
    
    /**
     * @dev Get user balance
     * @param user User address
     * @return User's balance
     */
    function getBalance(address user) external view returns (uint256) {
        return balances[user];
    }
    
    /**
     * @dev Get contract balance
     * @return Contract's ETH balance
     */
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    /**
     * @dev Batch deposit function with loop vulnerability
     * @param amounts Array of amounts to deposit
     */
    function batchDeposit(uint256[] calldata amounts) external payable {
        uint256 totalAmount = 0;
        
        // VULNERABILITY: Unbounded loop, potential DoS
        for (uint256 i = 0; i < amounts.length; i++) {
            require(amounts[i] > 0, "All amounts must be greater than 0");
            totalAmount += amounts[i];
            balances[msg.sender] += amounts[i];
        }
        
        require(msg.value == totalAmount, "Sent value must match total amount");
        totalDeposits += totalAmount;
        
        emit Deposit(msg.sender, totalAmount);
    }
    
    /**
     * @dev Calculate interest with precision issues
     * @param principal Principal amount
     * @param rate Interest rate (in basis points, e.g., 500 = 5%)
     * @param time Time period
     * @return Interest amount
     */
    function calculateInterest(uint256 principal, uint256 rate, uint256 time) 
        external 
        pure 
        returns (uint256) 
    {
        // VULNERABILITY: Integer division precision loss
        return (principal * rate * time) / 10000;
    }
    
    /**
     * @dev Fallback function to receive ETH
     */
    receive() external payable {
        // Accept ETH without updating balances - potential issue
    }
}
