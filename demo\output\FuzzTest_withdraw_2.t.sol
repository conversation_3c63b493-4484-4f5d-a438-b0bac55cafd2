// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_withdraw_2 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_withdraw_pool_2() public {
        // Test pool 2: {'amount': 100}
        try target.withdraw(100) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}