"""
SMT translation and verification using Z3 or CVC5.

Translates Solidity assertions to SMT-LIB format and verifies them.
"""

import re
import subprocess
import tempfile
from pathlib import Path
from typing import Optional, Tuple, Dict, Any
from rich.console import Console

from .models import SolverType

console = Console()


class SMTTranslator:
    """Translates Solidity assertions to SMT and verifies them."""
    
    def __init__(self, solver: SolverType = SolverType.Z3, verbose: bool = False):
        self.solver = solver
        self.verbose = verbose
        
        # Check if solver is available
        self._check_solver_availability()
    
    def _check_solver_availability(self) -> None:
        """Check if the specified SMT solver is available."""
        try:
            if self.solver == SolverType.Z3:
                import z3
            else:  # CVC5
                result = subprocess.run(["cvc5", "--version"], capture_output=True)
                if result.returncode != 0:
                    raise RuntimeError("CVC5 not found in PATH")
        except ImportError:
            raise RuntimeError(f"SMT solver {self.solver} not available")
        except Exception as e:
            raise RuntimeError(f"SMT solver {self.solver} not available: {e}")
    
    def translate_assertion(self, solidity_assertion: str) -> Optional[str]:
        """Translate a Solidity assertion to SMT-LIB format."""
        try:
            # Clean up the assertion
            assertion = solidity_assertion.strip()
            
            # Extract condition from require() or assert()
            condition = self._extract_condition(assertion)
            if not condition:
                return None
            
            # Translate to SMT
            smt_formula = self._translate_condition_to_smt(condition)
            return smt_formula
            
        except Exception as e:
            if self.verbose:
                console.print(f"Translation failed: {e}")
            return None
    
    def _extract_condition(self, assertion: str) -> Optional[str]:
        """Extract the condition from require() or assert() statements."""
        # Match require(condition, message) or assert(condition)
        patterns = [
            r'require\s*\(\s*([^,)]+)(?:\s*,\s*[^)]+)?\s*\)',
            r'assert\s*\(\s*([^)]+)\s*\)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, assertion, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _translate_condition_to_smt(self, condition: str) -> str:
        """Translate a Solidity condition to SMT-LIB format."""
        # This is a simplified translation - a full implementation would need
        # a proper Solidity parser and type system
        
        # Basic variable declarations (assuming common types)
        declarations = []
        variables = self._extract_variables(condition)
        
        for var in variables:
            if var in ['msg.sender', 'msg.value', 'block.timestamp']:
                declarations.append(f"(declare-const {var.replace('.', '_')} Int)")
            elif 'balance' in var.lower():
                declarations.append(f"(declare-const {var} Int)")
            else:
                declarations.append(f"(declare-const {var} Int)")
        
        # Translate operators
        smt_condition = condition
        smt_condition = smt_condition.replace('&&', ' and ')
        smt_condition = smt_condition.replace('||', ' or ')
        smt_condition = smt_condition.replace('!', ' not ')
        smt_condition = smt_condition.replace('==', '=')
        smt_condition = smt_condition.replace('!=', 'not (=')
        smt_condition = smt_condition.replace('msg.sender', 'msg_sender')
        smt_condition = smt_condition.replace('msg.value', 'msg_value')
        smt_condition = smt_condition.replace('block.timestamp', 'block_timestamp')
        
        # Handle != operator properly
        if 'not (=' in smt_condition and not smt_condition.count('(') == smt_condition.count(')'):
            smt_condition = smt_condition.replace('not (=', 'not (= ') + ')'
        
        # Build complete SMT formula
        smt_formula = "\n".join([
            "(set-logic QF_LIA)",
            *declarations,
            f"(assert (not ({smt_condition})))",
            "(check-sat)",
            "(get-model)"
        ])
        
        return smt_formula
    
    def _extract_variables(self, condition: str) -> set:
        """Extract variable names from a condition."""
        # Simple regex to find identifiers
        variables = set()
        
        # Find all identifiers
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*\b', condition)
        
        for identifier in identifiers:
            # Skip Solidity keywords and functions
            if identifier not in ['require', 'assert', 'true', 'false', 'and', 'or', 'not']:
                variables.add(identifier)
        
        return variables
    
    def verify_formula(self, smt_formula: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Verify an SMT formula and return result with counterexample if any."""
        if self.solver == SolverType.Z3:
            return self._verify_with_z3(smt_formula)
        else:
            return self._verify_with_cvc5(smt_formula)
    
    def _verify_with_z3(self, smt_formula: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Verify formula using Z3 Python API."""
        try:
            import z3
            
            # Parse SMT formula
            solver = z3.Solver()
            
            # This is a simplified approach - ideally we'd parse the SMT-LIB properly
            # For now, we'll use a basic approach
            
            # Extract variable declarations and assertions
            lines = smt_formula.split('\n')
            variables = {}
            
            for line in lines:
                line = line.strip()
                if line.startswith('(declare-const'):
                    # Extract variable name
                    match = re.search(r'\(declare-const\s+(\w+)\s+Int\)', line)
                    if match:
                        var_name = match.group(1)
                        variables[var_name] = z3.Int(var_name)
                elif line.startswith('(assert'):
                    # This is very simplified - a real implementation would need proper parsing
                    if self.verbose:
                        console.print(f"Processing assertion: {line}")
            
            # Check satisfiability
            result = solver.check()
            
            if result == z3.sat:
                # Formula is satisfiable (invariant can be violated)
                model = solver.model()
                counterexample = {}
                for var_name, var in variables.items():
                    if model[var] is not None:
                        counterexample[var_name] = str(model[var])
                return False, counterexample
            elif result == z3.unsat:
                # Formula is unsatisfiable (invariant holds)
                return True, None
            else:
                # Unknown result
                return False, {"error": "Unknown result from Z3"}
                
        except Exception as e:
            if self.verbose:
                console.print(f"Z3 verification failed: {e}")
            return False, {"error": str(e)}
    
    def _verify_with_cvc5(self, smt_formula: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Verify formula using CVC5 command line."""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.smt2', delete=False) as tmp_file:
                tmp_file.write(smt_formula)
                tmp_path = Path(tmp_file.name)
            
            # Run CVC5
            result = subprocess.run(
                ["cvc5", str(tmp_path)],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            # Clean up
            tmp_path.unlink()
            
            output = result.stdout.strip()
            
            if "unsat" in output:
                # Formula is unsatisfiable (invariant holds)
                return True, None
            elif "sat" in output:
                # Formula is satisfiable (invariant can be violated)
                # Try to extract model
                counterexample = self._parse_cvc5_model(output)
                return False, counterexample
            else:
                return False, {"error": f"Unexpected CVC5 output: {output}"}
                
        except subprocess.TimeoutExpired:
            return False, {"error": "CVC5 verification timed out"}
        except Exception as e:
            if self.verbose:
                console.print(f"CVC5 verification failed: {e}")
            return False, {"error": str(e)}
    
    def _parse_cvc5_model(self, output: str) -> Dict[str, Any]:
        """Parse CVC5 model output to extract variable assignments."""
        model = {}
        
        # Look for variable assignments in the output
        lines = output.split('\n')
        for line in lines:
            # Simple parsing - look for patterns like "(define-fun var () Int value)"
            match = re.search(r'\(define-fun\s+(\w+)\s+\(\)\s+Int\s+([^)]+)\)', line)
            if match:
                var_name = match.group(1)
                value = match.group(2).strip()
                model[var_name] = value
        
        return model if model else {"raw_output": output}
