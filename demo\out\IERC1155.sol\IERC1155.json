{"abi": [{"type": "function", "name": "balanceOf", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_id", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOfBatch", "inputs": [{"name": "_owners", "type": "address[]", "internalType": "address[]"}, {"name": "_ids", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "isApprovedForAll", "inputs": [{"name": "_owner", "type": "address", "internalType": "address"}, {"name": "_operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "safeBatchTransferFrom", "inputs": [{"name": "_from", "type": "address", "internalType": "address"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "_ids", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "_values", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "_from", "type": "address", "internalType": "address"}, {"name": "_to", "type": "address", "internalType": "address"}, {"name": "_id", "type": "uint256", "internalType": "uint256"}, {"name": "_value", "type": "uint256", "internalType": "uint256"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setApprovalForAll", "inputs": [{"name": "_operator", "type": "address", "internalType": "address"}, {"name": "_approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceID", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "ApprovalForAll", "inputs": [{"name": "_owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "TransferBatch", "inputs": [{"name": "_operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_ids", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}, {"name": "_values", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "TransferSingle", "inputs": [{"name": "_operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_id", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "_value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "URI", "inputs": [{"name": "_value", "type": "string", "indexed": false, "internalType": "string"}, {"name": "_id", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"balanceOf(address,uint256)": "00fdd58e", "balanceOfBatch(address[],uint256[])": "4e1273f4", "isApprovedForAll(address,address)": "e985e9c5", "safeBatchTransferFrom(address,address,uint256[],uint256[],bytes)": "2eb2c2d6", "safeTransferFrom(address,address,uint256,uint256,bytes)": "f242432a", "setApprovalForAll(address,bool)": "a22cb465", "supportsInterface(bytes4)": "01ffc9a7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"_ids\",\"type\":\"uint256[]\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"_values\",\"type\":\"uint256[]\"}],\"name\":\"TransferBatch\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_id\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"}],\"name\":\"TransferSingle\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"_value\",\"type\":\"string\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_id\",\"type\":\"uint256\"}],\"name\":\"URI\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_id\",\"type\":\"uint256\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_owners\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"_ids\",\"type\":\"uint256[]\"}],\"name\":\"balanceOfBatch\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"uint256[]\",\"name\":\"_ids\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"_values\",\"type\":\"uint256[]\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"safeBatchTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_id\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"_value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"_approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceID\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"See https://eips.ethereum.org/EIPS/eip-1155 Note: The ERC-165 identifier for this interface is 0xd9b67a26.\",\"events\":{\"ApprovalForAll(address,address,bool)\":{\"details\":\"MUST emit when approval for a second party/operator address to manage all tokens for an owner address is enabled or disabled (absence of an event assumes disabled).\"},\"TransferBatch(address,address,address,uint256[],uint256[])\":{\"details\":\"- Either `TransferSingle` or `TransferBatch` MUST emit when tokens are transferred, including zero value transfers as well as minting or burning (see \\\"Safe Transfer Rules\\\" section of the standard). - The `_operator` argument MUST be the address of an account/contract that is approved to make the transfer (SHOULD be msg.sender). - The `_from` argument MUST be the address of the holder whose balance is decreased. - The `_to` argument MUST be the address of the recipient whose balance is increased. - The `_ids` argument MUST be the list of tokens being transferred. - The `_values` argument MUST be the list of number of tokens (matching the list and order of tokens specified in _ids) the holder balance is decreased by and match what the recipient balance is increased by. - When minting/creating tokens, the `_from` argument MUST be set to `0x0` (i.e. zero address). - When burning/destroying tokens, the `_to` argument MUST be set to `0x0` (i.e. zero address).\"},\"TransferSingle(address,address,address,uint256,uint256)\":{\"details\":\"- Either `TransferSingle` or `TransferBatch` MUST emit when tokens are transferred, including zero value transfers as well as minting or burning (see \\\"Safe Transfer Rules\\\" section of the standard). - The `_operator` argument MUST be the address of an account/contract that is approved to make the transfer (SHOULD be msg.sender). - The `_from` argument MUST be the address of the holder whose balance is decreased. - The `_to` argument MUST be the address of the recipient whose balance is increased. - The `_id` argument MUST be the token type being transferred. - The `_value` argument MUST be the number of tokens the holder balance is decreased by and match what the recipient balance is increased by. - When minting/creating tokens, the `_from` argument MUST be set to `0x0` (i.e. zero address). - When burning/destroying tokens, the `_to` argument MUST be set to `0x0` (i.e. zero address).\"},\"URI(string,uint256)\":{\"details\":\"MUST emit when the URI is updated for a token ID. URIs are defined in RFC 3986. The URI MUST point to a JSON file that conforms to the \\\"ERC-1155 Metadata URI JSON Schema\\\".\"}},\"kind\":\"dev\",\"methods\":{\"balanceOf(address,uint256)\":{\"params\":{\"_id\":\"ID of the token\",\"_owner\":\"The address of the token holder\"},\"returns\":{\"_0\":\"The _owner's balance of the token type requested\"}},\"balanceOfBatch(address[],uint256[])\":{\"params\":{\"_ids\":\"ID of the tokens\",\"_owners\":\"The addresses of the token holders\"},\"returns\":{\"_0\":\"The _owner's balance of the token types requested (i.e. balance for each (owner, id) pair)\"}},\"isApprovedForAll(address,address)\":{\"params\":{\"_operator\":\"Address of authorized operator\",\"_owner\":\"The owner of the tokens\"},\"returns\":{\"_0\":\"True if the operator is approved, false if not\"}},\"safeBatchTransferFrom(address,address,uint256[],uint256[],bytes)\":{\"details\":\"Caller must be approved to manage the tokens being transferred out of the `_from` account (see \\\"Approval\\\" section of the standard). - MUST revert if `_to` is the zero address. - MUST revert if length of `_ids` is not the same as length of `_values`. - MUST revert if any of the balance(s) of the holder(s) for token(s) in `_ids` is lower than the respective amount(s) in `_values` sent to the recipient. - MUST revert on any other error. - MUST emit `TransferSingle` or `TransferBatch` event(s) such that all the balance changes are reflected (see \\\"Safe Transfer Rules\\\" section of the standard). - Balance changes and events MUST follow the ordering of the arrays (_ids[0]/_values[0] before _ids[1]/_values[1], etc). - After the above conditions for the transfer(s) in the batch are met, this function MUST check if `_to` is a smart contract (e.g. code size > 0). If so, it MUST call the relevant `ERC1155TokenReceiver` hook(s) on `_to` and act appropriately (see \\\"Safe Transfer Rules\\\" section of the standard).\",\"params\":{\"_data\":\"Additional data with no specified format, MUST be sent unaltered in call to the `ERC1155TokenReceiver` hook(s) on `_to`\",\"_from\":\"Source address\",\"_ids\":\"IDs of each token type (order and length must match _values array)\",\"_to\":\"Target address\",\"_values\":\"Transfer amounts per token type (order and length must match _ids array)\"}},\"safeTransferFrom(address,address,uint256,uint256,bytes)\":{\"details\":\"Caller must be approved to manage the tokens being transferred out of the `_from` account (see \\\"Approval\\\" section of the standard). - MUST revert if `_to` is the zero address. - MUST revert if balance of holder for token `_id` is lower than the `_value` sent. - MUST revert on any other error. - MUST emit the `TransferSingle` event to reflect the balance change (see \\\"Safe Transfer Rules\\\" section of the standard). - After the above conditions are met, this function MUST check if `_to` is a smart contract (e.g. code size > 0). If so, it MUST call `onERC1155Received` on `_to` and act appropriately (see \\\"Safe Transfer Rules\\\" section of the standard).\",\"params\":{\"_data\":\"Additional data with no specified format, MUST be sent unaltered in call to `onERC1155Received` on `_to`\",\"_from\":\"Source address\",\"_id\":\"ID of the token type\",\"_to\":\"Target address\",\"_value\":\"Transfer amount\"}},\"setApprovalForAll(address,bool)\":{\"details\":\"MUST emit the ApprovalForAll event on success.\",\"params\":{\"_approved\":\"True if the operator is approved, false to revoke approval\",\"_operator\":\"Address to add to the set of authorized operators\"}},\"supportsInterface(bytes4)\":{\"details\":\"Interface identification is specified in ERC-165. This function uses less than 30,000 gas.\",\"params\":{\"interfaceID\":\"The interface identifier, as specified in ERC-165\"},\"returns\":{\"_0\":\"`true` if the contract implements `interfaceID` and `interfaceID` is not 0xffffffff, `false` otherwise\"}}},\"title\":\"ERC-1155 Multi Token Standard\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"balanceOf(address,uint256)\":{\"notice\":\"Get the balance of an account's tokens.\"},\"balanceOfBatch(address[],uint256[])\":{\"notice\":\"Get the balance of multiple account/token pairs\"},\"isApprovedForAll(address,address)\":{\"notice\":\"Queries the approval status of an operator for a given owner.\"},\"safeBatchTransferFrom(address,address,uint256[],uint256[],bytes)\":{\"notice\":\"Transfers `_values` amount(s) of `_ids` from the `_from` address to the `_to` address specified (with safety call).\"},\"safeTransferFrom(address,address,uint256,uint256,bytes)\":{\"notice\":\"Transfers `_value` amount of an `_id` from the `_from` address to the `_to` address specified (with safety call).\"},\"setApprovalForAll(address,bool)\":{\"notice\":\"Enable or disable approval for a third party (\\\"operator\\\") to manage all of the caller's tokens.\"},\"supportsInterface(bytes4)\":{\"notice\":\"Query if a contract implements an interface\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/interfaces/IERC1155.sol\":\"IERC1155\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/interfaces/IERC1155.sol\":{\"keccak256\":\"0x97d75dd4dc392a97b58bb477c3a9edea4f6f771c1db126343b5c8b22f1b2374b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b8a1967b0f520b25486b7908f9b9dfb47011707c73d2b16a35b50d27d4d3b54\",\"dweb:/ipfs/QmbW4C9pGdzUzsZu71HAnddSdugNEA2BS17mo3uCZb1Jd6\"]},\"lib/forge-std/src/interfaces/IERC165.sol\":{\"keccak256\":\"0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc\",\"dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "_operator", "type": "address", "indexed": true}, {"internalType": "bool", "name": "_approved", "type": "bool", "indexed": false}], "type": "event", "name": "ApprovalForAll", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_operator", "type": "address", "indexed": true}, {"internalType": "address", "name": "_from", "type": "address", "indexed": true}, {"internalType": "address", "name": "_to", "type": "address", "indexed": true}, {"internalType": "uint256[]", "name": "_ids", "type": "uint256[]", "indexed": false}, {"internalType": "uint256[]", "name": "_values", "type": "uint256[]", "indexed": false}], "type": "event", "name": "TransferBatch", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_operator", "type": "address", "indexed": true}, {"internalType": "address", "name": "_from", "type": "address", "indexed": true}, {"internalType": "address", "name": "_to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "_id", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "_value", "type": "uint256", "indexed": false}], "type": "event", "name": "TransferSingle", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "_value", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "_id", "type": "uint256", "indexed": true}], "type": "event", "name": "URI", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address[]", "name": "_owners", "type": "address[]"}, {"internalType": "uint256[]", "name": "_ids", "type": "uint256[]"}], "stateMutability": "view", "type": "function", "name": "balanceOfBatch", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256[]", "name": "_ids", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_values", "type": "uint256[]"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "safeBatchTransferFrom"}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "safeTransferFrom"}, {"inputs": [{"internalType": "address", "name": "_operator", "type": "address"}, {"internalType": "bool", "name": "_approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setApprovalForAll"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"balanceOf(address,uint256)": {"params": {"_id": "ID of the token", "_owner": "The address of the token holder"}, "returns": {"_0": "The _owner's balance of the token type requested"}}, "balanceOfBatch(address[],uint256[])": {"params": {"_ids": "ID of the tokens", "_owners": "The addresses of the token holders"}, "returns": {"_0": "The _owner's balance of the token types requested (i.e. balance for each (owner, id) pair)"}}, "isApprovedForAll(address,address)": {"params": {"_operator": "Address of authorized operator", "_owner": "The owner of the tokens"}, "returns": {"_0": "True if the operator is approved, false if not"}}, "safeBatchTransferFrom(address,address,uint256[],uint256[],bytes)": {"details": "Caller must be approved to manage the tokens being transferred out of the `_from` account (see \"Approval\" section of the standard). - MUST revert if `_to` is the zero address. - MUST revert if length of `_ids` is not the same as length of `_values`. - MUST revert if any of the balance(s) of the holder(s) for token(s) in `_ids` is lower than the respective amount(s) in `_values` sent to the recipient. - MUST revert on any other error. - MUST emit `TransferSingle` or `TransferBatch` event(s) such that all the balance changes are reflected (see \"Safe Transfer Rules\" section of the standard). - Balance changes and events MUST follow the ordering of the arrays (_ids[0]/_values[0] before _ids[1]/_values[1], etc). - After the above conditions for the transfer(s) in the batch are met, this function MUST check if `_to` is a smart contract (e.g. code size > 0). If so, it MUST call the relevant `ERC1155TokenReceiver` hook(s) on `_to` and act appropriately (see \"Safe Transfer Rules\" section of the standard).", "params": {"_data": "Additional data with no specified format, MUST be sent unaltered in call to the `ERC1155TokenReceiver` hook(s) on `_to`", "_from": "Source address", "_ids": "IDs of each token type (order and length must match _values array)", "_to": "Target address", "_values": "Transfer amounts per token type (order and length must match _ids array)"}}, "safeTransferFrom(address,address,uint256,uint256,bytes)": {"details": "Caller must be approved to manage the tokens being transferred out of the `_from` account (see \"Approval\" section of the standard). - MUST revert if `_to` is the zero address. - MUST revert if balance of holder for token `_id` is lower than the `_value` sent. - MUST revert on any other error. - MUST emit the `TransferSingle` event to reflect the balance change (see \"Safe Transfer Rules\" section of the standard). - After the above conditions are met, this function MUST check if `_to` is a smart contract (e.g. code size > 0). If so, it MUST call `onERC1155Received` on `_to` and act appropriately (see \"Safe Transfer Rules\" section of the standard).", "params": {"_data": "Additional data with no specified format, MUST be sent unaltered in call to `onERC1155Received` on `_to`", "_from": "Source address", "_id": "ID of the token type", "_to": "Target address", "_value": "Transfer amount"}}, "setApprovalForAll(address,bool)": {"details": "MUST emit the ApprovalForAll event on success.", "params": {"_approved": "True if the operator is approved, false to revoke approval", "_operator": "Address to add to the set of authorized operators"}}, "supportsInterface(bytes4)": {"details": "Interface identification is specified in ERC-165. This function uses less than 30,000 gas.", "params": {"interfaceID": "The interface identifier, as specified in ERC-165"}, "returns": {"_0": "`true` if the contract implements `interfaceID` and `interfaceID` is not 0xffffffff, `false` otherwise"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"balanceOf(address,uint256)": {"notice": "Get the balance of an account's tokens."}, "balanceOfBatch(address[],uint256[])": {"notice": "Get the balance of multiple account/token pairs"}, "isApprovedForAll(address,address)": {"notice": "Queries the approval status of an operator for a given owner."}, "safeBatchTransferFrom(address,address,uint256[],uint256[],bytes)": {"notice": "Transfers `_values` amount(s) of `_ids` from the `_from` address to the `_to` address specified (with safety call)."}, "safeTransferFrom(address,address,uint256,uint256,bytes)": {"notice": "Transfers `_value` amount of an `_id` from the `_from` address to the `_to` address specified (with safety call)."}, "setApprovalForAll(address,bool)": {"notice": "Enable or disable approval for a third party (\"operator\") to manage all of the caller's tokens."}, "supportsInterface(bytes4)": {"notice": "Query if a contract implements an interface"}}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/interfaces/IERC1155.sol": "IERC1155"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"lib/forge-std/src/interfaces/IERC1155.sol": {"keccak256": "0x97d75dd4dc392a97b58bb477c3a9edea4f6f771c1db126343b5c8b22f1b2374b", "urls": ["bzz-raw://9b8a1967b0f520b25486b7908f9b9dfb47011707c73d2b16a35b50d27d4d3b54", "dweb:/ipfs/QmbW4C9pGdzUzsZu71HAnddSdugNEA2BS17mo3uCZb1Jd6"], "license": "MIT"}, "lib/forge-std/src/interfaces/IERC165.sol": {"keccak256": "0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600", "urls": ["bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc", "dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT"], "license": "MIT"}}, "version": 1}, "id": 19}