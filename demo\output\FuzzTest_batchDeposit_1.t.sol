// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_batchDeposit_1 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_batchDeposit_pool_1() public {
        // Test pool 1: {'amounts': [100, 200, 300]}
        try target.batchDeposit([100, 200, 300]) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}