{"abi": [{"type": "function", "name": "asset", "inputs": [], "outputs": [{"name": "assetTokenAddress", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "claimableDepositRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "claimableAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "claimableRedeemRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "claimableShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToAssets", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToShares", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "isOperator", "inputs": [{"name": "controller", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "status", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "maxDeposit", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "maxAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "maxMint", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "maxShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON>", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "maxShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "max<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "maxAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "pendingDepositRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "pendingAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "pendingRedeemRequest", "inputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}], "outputs": [{"name": "pendingShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewDeposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewMint", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewRedeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "previewWithdraw", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "redeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "requestDeposit", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "requestRedeem", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}, {"name": "controller", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "requestId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperator", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "share", "inputs": [], "outputs": [{"name": "shareTokenAddress", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceID", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "totalAssets", "inputs": [], "outputs": [{"name": "totalManagedAssets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "DepositRequest", "inputs": [{"name": "controller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "sender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OperatorSet", "inputs": [{"name": "controller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "RedeemRequest", "inputs": [{"name": "controller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "requestId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "sender", "type": "address", "indexed": false, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Withdraw", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "receiver", "type": "address", "indexed": true, "internalType": "address"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"asset()": "38d52e0f", "claimableDepositRequest(uint256,address)": "995ea21a", "claimableRedeemRequest(uint256,address)": "eaed1d07", "convertToAssets(uint256)": "07a2d13a", "convertToShares(uint256)": "c6e6f592", "deposit(uint256,address)": "6e553f65", "deposit(uint256,address,address)": "2e2d2984", "isOperator(address,address)": "b6363cf2", "maxDeposit(address)": "402d267d", "maxMint(address)": "c63d75b6", "maxRedeem(address)": "d905777e", "maxWithdraw(address)": "ce96cb77", "mint(uint256,address)": "94bf804d", "mint(uint256,address,address)": "da39b3e7", "pendingDepositRequest(uint256,address)": "26c6f96c", "pendingRedeemRequest(uint256,address)": "f5a23d8d", "previewDeposit(uint256)": "ef8b30f7", "previewMint(uint256)": "b3d7f6b9", "previewRedeem(uint256)": "4cdad506", "previewWithdraw(uint256)": "0a28a477", "redeem(uint256,address,address)": "ba087652", "requestDeposit(uint256,address,address)": "85b77f45", "requestRedeem(uint256,address,address)": "7d41c86e", "setOperator(address,bool)": "558a7297", "share()": "a8d5fd65", "supportsInterface(bytes4)": "01ffc9a7", "totalAssets()": "01e1d114", "withdraw(uint256,address,address)": "b460af94"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Deposit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"DepositRequest\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"OperatorSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"RedeemRequest\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Withdraw\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"asset\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"assetTokenAddress\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"claimableDepositRequest\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"claimableAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"claimableRedeemRequest\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"claimableShares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"convertToAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"convertToShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"maxDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"maxAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"maxMint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"maxShares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"maxRedeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"maxShares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"maxWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"maxAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"mint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"mint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"pendingDepositRequest\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"pendingAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"}],\"name\":\"pendingRedeemRequest\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"pendingShares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"previewDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"previewMint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"previewRedeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"previewWithdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"redeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"requestDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"controller\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"requestRedeem\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"share\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"shareTokenAddress\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceID\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"totalManagedAssets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"withdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Interface of the fully asynchronous Vault interface of ERC7540, as defined in https://eips.ethereum.org/EIPS/eip-7540\",\"events\":{\"OperatorSet(address,address,bool)\":{\"details\":\"The event emitted when an operator is set.\",\"params\":{\"approved\":\"The approval status.\",\"controller\":\"The address of the controller.\",\"operator\":\"The address of the operator.\"}}},\"kind\":\"dev\",\"methods\":{\"asset()\":{\"details\":\"Returns the address of the underlying token used for the Vault for accounting, depositing, and withdrawing. - MUST be an ERC-20 token contract. - MUST NOT revert.\"},\"claimableDepositRequest(uint256,address)\":{\"details\":\"Returns the amount of requested assets in Claimable state for the controller to deposit or mint. - MUST NOT include any assets in Pending state. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input.\"},\"claimableRedeemRequest(uint256,address)\":{\"details\":\"Returns the amount of requested shares in Claimable state for the controller to redeem or withdraw. - MUST NOT include any shares in Pending state for redeem or withdraw. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input.\"},\"convertToAssets(uint256)\":{\"details\":\"Returns the amount of assets that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the \\u201cper-user\\u201d price-per-share, and instead should reflect the \\u201caverage-user\\u2019s\\u201d price-per-share, meaning what the average user should expect to see when exchanging to and from.\"},\"convertToShares(uint256)\":{\"details\":\"Returns the amount of shares that the Vault would exchange for the amount of assets provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the \\u201cper-user\\u201d price-per-share, and instead should reflect the \\u201caverage-user\\u2019s\\u201d price-per-share, meaning what the average user should expect to see when exchanging to and from.\"},\"deposit(uint256,address)\":{\"details\":\"Mints shares Vault shares to receiver by depositing exactly amount of underlying tokens. - MUST emit the Deposit event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   deposit execution, and are accounted for during deposit. - MUST revert if all of assets cannot be deposited (due to deposit limit being reached, slippage, the user not   approving enough underlying tokens to the Vault contract, etc). NOTE: most implementations will require pre-approval of the Vault with the Vault\\u2019s underlying asset token.\"},\"deposit(uint256,address,address)\":{\"details\":\"Mints shares Vault shares to receiver by claiming the Request of the controller. - MUST emit the Deposit event. - controller MUST equal msg.sender unless the controller has approved the msg.sender as an operator.\"},\"isOperator(address,address)\":{\"details\":\"Returns `true` if the `operator` is approved as an operator for an `controller`.\",\"params\":{\"controller\":\"The address of the controller.\",\"operator\":\"The address of the operator.\"},\"returns\":{\"status\":\"The approval status\"}},\"maxDeposit(address)\":{\"details\":\"Returns the maximum amount of the underlying asset that can be deposited into the Vault for the receiver, through a deposit call. - MUST return a limited value if receiver is subject to some deposit limit. - MUST return 2 ** 256 - 1 if there is no limit on the maximum amount of assets that may be deposited. - MUST NOT revert.\"},\"maxMint(address)\":{\"details\":\"Returns the maximum amount of the Vault shares that can be minted for the receiver, through a mint call. - MUST return a limited value if receiver is subject to some mint limit. - MUST return 2 ** 256 - 1 if there is no limit on the maximum amount of shares that may be minted. - MUST NOT revert.\"},\"maxRedeem(address)\":{\"details\":\"Returns the maximum amount of Vault shares that can be redeemed from the owner balance in the Vault, through a redeem call. - MUST return a limited value if owner is subject to some withdrawal limit or timelock. - MUST return balanceOf(owner) if owner is not subject to any withdrawal limit or timelock. - MUST NOT revert.\"},\"maxWithdraw(address)\":{\"details\":\"Returns the maximum amount of the underlying asset that can be withdrawn from the owner balance in the Vault, through a withdraw call. - MUST return a limited value if owner is subject to some withdrawal limit or timelock. - MUST NOT revert.\"},\"mint(uint256,address)\":{\"details\":\"Mints exactly shares Vault shares to receiver by depositing amount of underlying tokens. - MUST emit the Deposit event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the mint   execution, and are accounted for during mint. - MUST revert if all of shares cannot be minted (due to deposit limit being reached, slippage, the user not   approving enough underlying tokens to the Vault contract, etc). NOTE: most implementations will require pre-approval of the Vault with the Vault\\u2019s underlying asset token.\"},\"mint(uint256,address,address)\":{\"details\":\"Mints exactly shares Vault shares to receiver by claiming the Request of the controller. - MUST emit the Deposit event. - controller MUST equal msg.sender unless the controller has approved the msg.sender as an operator.\"},\"pendingDepositRequest(uint256,address)\":{\"details\":\"Returns the amount of requested assets in Pending state. - MUST NOT include any assets in Claimable state for deposit or mint. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input.\"},\"pendingRedeemRequest(uint256,address)\":{\"details\":\"Returns the amount of requested shares in Pending state. - MUST NOT include any shares in Claimable state for redeem or withdraw. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input.\"},\"previewDeposit(uint256)\":{\"details\":\"Allows an on-chain or off-chain user to simulate the effects of their deposit at the current block, given current on-chain conditions. - MUST return as close to and no more than the exact amount of Vault shares that would be minted in a deposit   call in the same transaction. I.e. deposit should return the same or more shares as previewDeposit if called   in the same transaction. - MUST NOT account for deposit limits like those returned from maxDeposit and should always act as though the   deposit would be accepted, regardless if the user has enough tokens approved, etc. - MUST be inclusive of deposit fees. Integrators should be aware of the existence of deposit fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToShares and previewDeposit SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by depositing.\"},\"previewMint(uint256)\":{\"details\":\"Allows an on-chain or off-chain user to simulate the effects of their mint at the current block, given current on-chain conditions. - MUST return as close to and no fewer than the exact amount of assets that would be deposited in a mint call   in the same transaction. I.e. mint should return the same or fewer assets as previewMint if called in the   same transaction. - MUST NOT account for mint limits like those returned from maxMint and should always act as though the mint   would be accepted, regardless if the user has enough tokens approved, etc. - MUST be inclusive of deposit fees. Integrators should be aware of the existence of deposit fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToAssets and previewMint SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by minting.\"},\"previewRedeem(uint256)\":{\"details\":\"Allows an on-chain or off-chain user to simulate the effects of their redeemption at the current block, given current on-chain conditions. - MUST return as close to and no more than the exact amount of assets that would be withdrawn in a redeem call   in the same transaction. I.e. redeem should return the same or more assets as previewRedeem if called in the   same transaction. - MUST NOT account for redemption limits like those returned from maxRedeem and should always act as though the   redemption would be accepted, regardless if the user has enough shares, etc. - MUST be inclusive of withdrawal fees. Integrators should be aware of the existence of withdrawal fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToAssets and previewRedeem SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by redeeming.\"},\"previewWithdraw(uint256)\":{\"details\":\"Allows an on-chain or off-chain user to simulate the effects of their withdrawal at the current block, given current on-chain conditions. - MUST return as close to and no fewer than the exact amount of Vault shares that would be burned in a withdraw   call in the same transaction. I.e. withdraw should return the same or fewer shares as previewWithdraw if   called   in the same transaction. - MUST NOT account for withdrawal limits like those returned from maxWithdraw and should always act as though   the withdrawal would be accepted, regardless if the user has enough shares, etc. - MUST be inclusive of withdrawal fees. Integrators should be aware of the existence of withdrawal fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToShares and previewWithdraw SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by depositing.\"},\"redeem(uint256,address,address)\":{\"details\":\"Burns exactly shares from owner and sends assets of underlying tokens to receiver. - MUST emit the Withdraw event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   redeem execution, and are accounted for during redeem. - MUST revert if all of shares cannot be redeemed (due to withdrawal limit being reached, slippage, the owner   not having enough shares, etc). NOTE: some implementations will require pre-requesting to the Vault before a withdrawal may be performed. Those methods should be performed separately.\"},\"requestDeposit(uint256,address,address)\":{\"details\":\"Transfers assets from sender into the Vault and submits a Request for asynchronous deposit. - MUST support ERC-20 approve / transferFrom on asset as a deposit Request flow. - MUST revert if all of assets cannot be requested for deposit. - owner MUST be msg.sender unless some unspecified explicit approval is given by the caller,    approval of ERC-20 tokens from owner to sender is NOT enough.\",\"params\":{\"assets\":\"the amount of deposit assets to transfer from owner\",\"controller\":\"the controller of the request who will be able to operate the request\",\"owner\":\"the source of the deposit assets NOTE: most implementations will require pre-approval of the Vault with the Vault's underlying asset token.\"}},\"requestRedeem(uint256,address,address)\":{\"details\":\"Assumes control of shares from sender into the Vault and submits a Request for asynchronous redeem. - MUST support a redeem Request flow where the control of shares is taken from sender directly   where msg.sender has ERC-20 approval over the shares of owner. - MUST revert if all of shares cannot be requested for redeem.\",\"params\":{\"controller\":\"the controller of the request who will be able to operate the request\",\"owner\":\"the source of the shares to be redeemed NOTE: most implementations will require pre-approval of the Vault with the Vault's share token.\",\"shares\":\"the amount of shares to be redeemed to transfer from owner\"}},\"setOperator(address,bool)\":{\"details\":\"Sets or removes an operator for the caller.\",\"params\":{\"approved\":\"The approval status.\",\"operator\":\"The address of the operator.\"},\"returns\":{\"_0\":\"Whether the call was executed successfully or not\"}},\"share()\":{\"details\":\"Returns the address of the share token - MUST be an ERC-20 token contract. - MUST NOT revert.\"},\"supportsInterface(bytes4)\":{\"details\":\"Interface identification is specified in ERC-165. This function uses less than 30,000 gas.\",\"params\":{\"interfaceID\":\"The interface identifier, as specified in ERC-165\"},\"returns\":{\"_0\":\"`true` if the contract implements `interfaceID` and `interfaceID` is not 0xffffffff, `false` otherwise\"}},\"totalAssets()\":{\"details\":\"Returns the total amount of the underlying asset that is \\u201cmanaged\\u201d by Vault. - SHOULD include any compounding that occurs from yield. - MUST be inclusive of any fees that are charged against assets in the Vault. - MUST NOT revert.\"},\"withdraw(uint256,address,address)\":{\"details\":\"Burns shares from owner and sends exactly assets of underlying tokens to receiver. - MUST emit the Withdraw event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   withdraw execution, and are accounted for during withdraw. - MUST revert if all of assets cannot be withdrawn (due to withdrawal limit being reached, slippage, the owner   not having enough shares, etc). Note that some implementations will require pre-requesting to the Vault before a withdrawal may be performed. Those methods should be performed separately.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"supportsInterface(bytes4)\":{\"notice\":\"Query if a contract implements an interface\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/interfaces/IERC7540.sol\":\"IERC7540\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/interfaces/IERC165.sol\":{\"keccak256\":\"0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc\",\"dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT\"]},\"lib/forge-std/src/interfaces/IERC7540.sol\":{\"keccak256\":\"0x9e130f02e48f3a015fd9e41428b1c8fb359bb7193bba4bd97f5e068af5903025\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://67ebd6a0f3d0197aa74fe905b3135bd555a387bbc9d53975b1bd2134644dbf94\",\"dweb:/ipfs/QmfJytpVRmpytBunkrGLiugW3tPBZn12A3Ak9LAcV6Tj3m\"]},\"lib/forge-std/src/interfaces/IERC7575.sol\":{\"keccak256\":\"0xb46aebdd749e632cf76466d2f75428f8e41e8283145818b621acd2624793782c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b32be8816d4658db66fdca29e9a9bc3a071bc47da19d6afa950eec5a32781ccb\",\"dweb:/ipfs/QmbJ27FAJz4rxV2xTzEPHkihYRrvhHH9mdkNSdxvba5Zxf\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "requestId", "type": "uint256", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}], "type": "event", "name": "DepositRequest", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address", "indexed": true}, {"internalType": "address", "name": "operator", "type": "address", "indexed": true}, {"internalType": "bool", "name": "approved", "type": "bool", "indexed": false}], "type": "event", "name": "OperatorSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "requestId", "type": "uint256", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}], "type": "event", "name": "RedeemRequest", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "receiver", "type": "address", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "Withdraw", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "asset", "outputs": [{"internalType": "address", "name": "assetTokenAddress", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "view", "type": "function", "name": "claimableDepositRequest", "outputs": [{"internalType": "uint256", "name": "claimableAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "view", "type": "function", "name": "claimableRedeemRequest", "outputs": [{"internalType": "uint256", "name": "claimableShares", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertToAssets", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "convertToShares", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "controller", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isOperator", "outputs": [{"internalType": "bool", "name": "status", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "view", "type": "function", "name": "maxDeposit", "outputs": [{"internalType": "uint256", "name": "maxAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "view", "type": "function", "name": "maxMint", "outputs": [{"internalType": "uint256", "name": "maxShares", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "max<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "maxShares", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "max<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "maxAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "mint", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "mint", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "view", "type": "function", "name": "pendingDepositRequest", "outputs": [{"internalType": "uint256", "name": "pendingAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}], "stateMutability": "view", "type": "function", "name": "pendingRedeemRequest", "outputs": [{"internalType": "uint256", "name": "pendingShares", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewDeposit", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewMint", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewRedeem", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "previewWithdraw", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "redeem", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "requestDeposit", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "address", "name": "controller", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "requestRedeem", "outputs": [{"internalType": "uint256", "name": "requestId", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "share", "outputs": [{"internalType": "address", "name": "shareTokenAddress", "type": "address"}]}, {"inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalAssets", "outputs": [{"internalType": "uint256", "name": "totalManagedAssets", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"asset()": {"details": "Returns the address of the underlying token used for the Vault for accounting, depositing, and withdrawing. - MUST be an ERC-20 token contract. - MUST NOT revert."}, "claimableDepositRequest(uint256,address)": {"details": "Returns the amount of requested assets in Claimable state for the controller to deposit or mint. - MUST NOT include any assets in Pending state. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input."}, "claimableRedeemRequest(uint256,address)": {"details": "Returns the amount of requested shares in Claimable state for the controller to redeem or withdraw. - MUST NOT include any shares in Pending state for redeem or withdraw. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input."}, "convertToAssets(uint256)": {"details": "Returns the amount of assets that the Vault would exchange for the amount of shares provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the “per-user” price-per-share, and instead should reflect the “average-user’s” price-per-share, meaning what the average user should expect to see when exchanging to and from."}, "convertToShares(uint256)": {"details": "Returns the amount of shares that the Vault would exchange for the amount of assets provided, in an ideal scenario where all the conditions are met. - MUST NOT be inclusive of any fees that are charged against assets in the Vault. - MUST NOT show any variations depending on the caller. - MUST NOT reflect slippage or other on-chain conditions, when performing the actual exchange. - MUST NOT revert. NOTE: This calculation MAY NOT reflect the “per-user” price-per-share, and instead should reflect the “average-user’s” price-per-share, meaning what the average user should expect to see when exchanging to and from."}, "deposit(uint256,address)": {"details": "Mints shares Vault shares to receiver by depositing exactly amount of underlying tokens. - MUST emit the Deposit event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   deposit execution, and are accounted for during deposit. - MUST revert if all of assets cannot be deposited (due to deposit limit being reached, slippage, the user not   approving enough underlying tokens to the Vault contract, etc). NOTE: most implementations will require pre-approval of the Vault with the Vault’s underlying asset token."}, "deposit(uint256,address,address)": {"details": "Mints shares Vault shares to receiver by claiming the Request of the controller. - MUST emit the Deposit event. - controller MUST equal msg.sender unless the controller has approved the msg.sender as an operator."}, "isOperator(address,address)": {"details": "Returns `true` if the `operator` is approved as an operator for an `controller`.", "params": {"controller": "The address of the controller.", "operator": "The address of the operator."}, "returns": {"status": "The approval status"}}, "maxDeposit(address)": {"details": "Returns the maximum amount of the underlying asset that can be deposited into the Vault for the receiver, through a deposit call. - MUST return a limited value if receiver is subject to some deposit limit. - MUST return 2 ** 256 - 1 if there is no limit on the maximum amount of assets that may be deposited. - MUST NOT revert."}, "maxMint(address)": {"details": "Returns the maximum amount of the Vault shares that can be minted for the receiver, through a mint call. - MUST return a limited value if receiver is subject to some mint limit. - MUST return 2 ** 256 - 1 if there is no limit on the maximum amount of shares that may be minted. - MUST NOT revert."}, "maxRedeem(address)": {"details": "Returns the maximum amount of Vault shares that can be redeemed from the owner balance in the Vault, through a redeem call. - MUST return a limited value if owner is subject to some withdrawal limit or timelock. - MUST return balanceOf(owner) if owner is not subject to any withdrawal limit or timelock. - MUST NOT revert."}, "maxWithdraw(address)": {"details": "Returns the maximum amount of the underlying asset that can be withdrawn from the owner balance in the Vault, through a withdraw call. - MUST return a limited value if owner is subject to some withdrawal limit or timelock. - MUST NOT revert."}, "mint(uint256,address)": {"details": "Mints exactly shares Vault shares to receiver by depositing amount of underlying tokens. - MUST emit the Deposit event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the mint   execution, and are accounted for during mint. - MUST revert if all of shares cannot be minted (due to deposit limit being reached, slippage, the user not   approving enough underlying tokens to the Vault contract, etc). NOTE: most implementations will require pre-approval of the Vault with the Vault’s underlying asset token."}, "mint(uint256,address,address)": {"details": "Mints exactly shares Vault shares to receiver by claiming the Request of the controller. - MUST emit the Deposit event. - controller MUST equal msg.sender unless the controller has approved the msg.sender as an operator."}, "pendingDepositRequest(uint256,address)": {"details": "Returns the amount of requested assets in Pending state. - MUST NOT include any assets in Claimable state for deposit or mint. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input."}, "pendingRedeemRequest(uint256,address)": {"details": "Returns the amount of requested shares in Pending state. - MUST NOT include any shares in Claimable state for redeem or withdraw. - MUST NOT show any variations depending on the caller. - MUST NOT revert unless due to integer overflow caused by an unreasonably large input."}, "previewDeposit(uint256)": {"details": "Allows an on-chain or off-chain user to simulate the effects of their deposit at the current block, given current on-chain conditions. - MUST return as close to and no more than the exact amount of Vault shares that would be minted in a deposit   call in the same transaction. I.e. deposit should return the same or more shares as previewDeposit if called   in the same transaction. - MUST NOT account for deposit limits like those returned from maxDeposit and should always act as though the   deposit would be accepted, regardless if the user has enough tokens approved, etc. - MUST be inclusive of deposit fees. Integrators should be aware of the existence of deposit fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToShares and previewDeposit SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by depositing."}, "previewMint(uint256)": {"details": "Allows an on-chain or off-chain user to simulate the effects of their mint at the current block, given current on-chain conditions. - MUST return as close to and no fewer than the exact amount of assets that would be deposited in a mint call   in the same transaction. I.e. mint should return the same or fewer assets as previewMint if called in the   same transaction. - MUST NOT account for mint limits like those returned from maxMint and should always act as though the mint   would be accepted, regardless if the user has enough tokens approved, etc. - MUST be inclusive of deposit fees. Integrators should be aware of the existence of deposit fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToAssets and previewMint SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by minting."}, "previewRedeem(uint256)": {"details": "Allows an on-chain or off-chain user to simulate the effects of their redeemption at the current block, given current on-chain conditions. - MUST return as close to and no more than the exact amount of assets that would be withdrawn in a redeem call   in the same transaction. I.e. redeem should return the same or more assets as previewRedeem if called in the   same transaction. - MUST NOT account for redemption limits like those returned from maxRedeem and should always act as though the   redemption would be accepted, regardless if the user has enough shares, etc. - MUST be inclusive of withdrawal fees. Integrators should be aware of the existence of withdrawal fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToAssets and previewRedeem SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by redeeming."}, "previewWithdraw(uint256)": {"details": "Allows an on-chain or off-chain user to simulate the effects of their withdrawal at the current block, given current on-chain conditions. - MUST return as close to and no fewer than the exact amount of Vault shares that would be burned in a withdraw   call in the same transaction. I.e. withdraw should return the same or fewer shares as previewWithdraw if   called   in the same transaction. - MUST NOT account for withdrawal limits like those returned from maxWithdraw and should always act as though   the withdrawal would be accepted, regardless if the user has enough shares, etc. - MUST be inclusive of withdrawal fees. Integrators should be aware of the existence of withdrawal fees. - MUST NOT revert. NOTE: any unfavorable discrepancy between convertToShares and previewWithdraw SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by depositing."}, "redeem(uint256,address,address)": {"details": "<PERSON> exactly shares from owner and sends assets of underlying tokens to receiver. - MUST emit the Withdraw event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   redeem execution, and are accounted for during redeem. - MUST revert if all of shares cannot be redeemed (due to withdrawal limit being reached, slippage, the owner   not having enough shares, etc). NOTE: some implementations will require pre-requesting to the Vault before a withdrawal may be performed. Those methods should be performed separately."}, "requestDeposit(uint256,address,address)": {"details": "Transfers assets from sender into the Vault and submits a Request for asynchronous deposit. - MUST support ERC-20 approve / transferFrom on asset as a deposit Request flow. - MUST revert if all of assets cannot be requested for deposit. - owner MUST be msg.sender unless some unspecified explicit approval is given by the caller,    approval of ERC-20 tokens from owner to sender is NOT enough.", "params": {"assets": "the amount of deposit assets to transfer from owner", "controller": "the controller of the request who will be able to operate the request", "owner": "the source of the deposit assets NOTE: most implementations will require pre-approval of the Vault with the Vault's underlying asset token."}}, "requestRedeem(uint256,address,address)": {"details": "Assumes control of shares from sender into the Vault and submits a Request for asynchronous redeem. - MUST support a redeem Request flow where the control of shares is taken from sender directly   where msg.sender has ERC-20 approval over the shares of owner. - MUST revert if all of shares cannot be requested for redeem.", "params": {"controller": "the controller of the request who will be able to operate the request", "owner": "the source of the shares to be redeemed NOTE: most implementations will require pre-approval of the Vault with the Vault's share token.", "shares": "the amount of shares to be redeemed to transfer from owner"}}, "setOperator(address,bool)": {"details": "Sets or removes an operator for the caller.", "params": {"approved": "The approval status.", "operator": "The address of the operator."}, "returns": {"_0": "Whether the call was executed successfully or not"}}, "share()": {"details": "Returns the address of the share token - MUST be an ERC-20 token contract. - MUST NOT revert."}, "supportsInterface(bytes4)": {"details": "Interface identification is specified in ERC-165. This function uses less than 30,000 gas.", "params": {"interfaceID": "The interface identifier, as specified in ERC-165"}, "returns": {"_0": "`true` if the contract implements `interfaceID` and `interfaceID` is not 0xffffffff, `false` otherwise"}}, "totalAssets()": {"details": "Returns the total amount of the underlying asset that is “managed” by Vault. - SHOULD include any compounding that occurs from yield. - MUST be inclusive of any fees that are charged against assets in the Vault. - MUST NOT revert."}, "withdraw(uint256,address,address)": {"details": "Burns shares from owner and sends exactly assets of underlying tokens to receiver. - MUST emit the Withdraw event. - MAY support an additional flow in which the underlying tokens are owned by the Vault contract before the   withdraw execution, and are accounted for during withdraw. - MUST revert if all of assets cannot be withdrawn (due to withdrawal limit being reached, slippage, the owner   not having enough shares, etc). Note that some implementations will require pre-requesting to the Vault before a withdrawal may be performed. Those methods should be performed separately."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"supportsInterface(bytes4)": {"notice": "Query if a contract implements an interface"}}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/interfaces/IERC7540.sol": "IERC7540"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"lib/forge-std/src/interfaces/IERC165.sol": {"keccak256": "0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600", "urls": ["bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc", "dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT"], "license": "MIT"}, "lib/forge-std/src/interfaces/IERC7540.sol": {"keccak256": "0x9e130f02e48f3a015fd9e41428b1c8fb359bb7193bba4bd97f5e068af5903025", "urls": ["bzz-raw://67ebd6a0f3d0197aa74fe905b3135bd555a387bbc9d53975b1bd2134644dbf94", "dweb:/ipfs/QmfJytpVRmpytBunkrGLiugW3tPBZn12A3Ak9LAcV6Tj3m"], "license": "MIT"}, "lib/forge-std/src/interfaces/IERC7575.sol": {"keccak256": "0xb46aebdd749e632cf76466d2f75428f8e41e8283145818b621acd2624793782c", "urls": ["bzz-raw://b32be8816d4658db66fdca29e9a9bc3a071bc47da19d6afa950eec5a32781ccb", "dweb:/ipfs/QmbJ27FAJz4rxV2xTzEPHkihYRrvhHH9mdkNSdxvba5Zxf"], "license": "MIT"}}, "version": 1}, "id": 25}