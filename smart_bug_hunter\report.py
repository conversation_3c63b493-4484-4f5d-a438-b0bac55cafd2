"""
Unified reporting system for smart-bug-hunter.

Generates Markdown reports combining fuzzing and invariant verification results.
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from rich.console import Console
from rich.markdown import Markdown

from .models import FuzzResults, InvariantResults, TestPool, Invariant

console = Console()


class ReportGenerator:
    """Generates unified reports from analysis results."""
    
    def __init__(self, output_dir: Optional[Path] = None, verbose: bool = False):
        self.output_dir = output_dir or Path("./output")
        self.verbose = verbose
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_unified_report(
        self,
        fuzz_results: Optional[FuzzResults] = None,
        invariant_results: Optional[InvariantResults] = None
    ) -> Path:
        """Generate a unified Markdown report from fuzzing and invariant results."""
        if self.verbose:
            console.print("📋 Generating unified report")
        
        # Load results from files if not provided
        if fuzz_results is None:
            fuzz_results = self._load_fuzz_results()
        
        if invariant_results is None:
            invariant_results = self._load_invariant_results()
        
        # Generate report content
        report_content = self._generate_report_content(fuzz_results, invariant_results)
        
        # Save report
        report_path = self.output_dir / "smart_bug_hunter_report.md"
        with open(report_path, "w") as f:
            f.write(report_content)
        
        if self.verbose:
            console.print(f"📄 Report saved to {report_path}")
            console.print(Markdown(report_content))
        
        return report_path
    
    def _load_fuzz_results(self) -> Optional[FuzzResults]:
        """Load fuzzing results from file."""
        fuzz_file = self.output_dir / "fuzz_results.json"
        if not fuzz_file.exists():
            return None
        
        try:
            with open(fuzz_file) as f:
                data = json.load(f)
            
            results = FuzzResults(
                contract_path=Path(data["contract_path"]),
                total_pools=data["total_pools"],
                execution_time=data.get("execution_time", 0.0)
            )
            
            # Load pools
            for pool_data in data.get("failed_pools", []):
                pool = TestPool(
                    id=pool_data["id"],
                    parameters=pool_data["parameters"],
                    parameter_names=pool_data["parameter_names"],
                    error_message=pool_data.get("error_message"),
                    execution_time=pool_data.get("execution_time", 0.0)
                )
                results.failed_pools.append(pool)
            
            for pool_data in data.get("error_pools", []):
                pool = TestPool(
                    id=pool_data["id"],
                    parameters=pool_data["parameters"],
                    parameter_names=pool_data["parameter_names"],
                    error_message=pool_data.get("error_message"),
                    execution_time=pool_data.get("execution_time", 0.0)
                )
                results.error_pools.append(pool)
            
            return results
            
        except Exception as e:
            if self.verbose:
                console.print(f"Failed to load fuzz results: {e}")
            return None
    
    def _load_invariant_results(self) -> Optional[InvariantResults]:
        """Load invariant results from file."""
        invariant_file = self.output_dir / "invariant_results.json"
        if not invariant_file.exists():
            return None
        
        try:
            with open(invariant_file) as f:
                data = json.load(f)
            
            results = InvariantResults(
                contract_path=Path(data["contract_path"]),
                total_invariants=data["total_invariants"],
                execution_time=data.get("execution_time", 0.0)
            )
            
            # Load invariants
            for inv_data in data.get("violations", []):
                invariant = Invariant(
                    id=inv_data["id"],
                    function_name=inv_data["function_name"],
                    type=inv_data["type"],
                    description=inv_data["description"],
                    solidity_assertion=inv_data["solidity_assertion"],
                    smt_formula=inv_data.get("smt_formula"),
                    is_verified=inv_data.get("is_verified"),
                    counterexample=inv_data.get("counterexample")
                )
                results.violations.append(invariant)
            
            return results
            
        except Exception as e:
            if self.verbose:
                console.print(f"Failed to load invariant results: {e}")
            return None
    
    def _generate_report_content(
        self,
        fuzz_results: Optional[FuzzResults],
        invariant_results: Optional[InvariantResults]
    ) -> str:
        """Generate the Markdown report content."""
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Start with header
        content = [
            "# Smart Bug Hunter Analysis Report",
            f"**Generated:** {now}",
            ""
        ]
        
        # Add summary section
        content.extend(self._generate_summary_section(fuzz_results, invariant_results))
        
        # Add fuzzing results section
        if fuzz_results:
            content.extend(self._generate_fuzzing_section(fuzz_results))
        
        # Add invariant results section
        if invariant_results:
            content.extend(self._generate_invariant_section(invariant_results))
        
        # Add recommendations section
        content.extend(self._generate_recommendations(fuzz_results, invariant_results))
        
        return "\n".join(content)
    
    def _generate_summary_section(
        self,
        fuzz_results: Optional[FuzzResults],
        invariant_results: Optional[InvariantResults]
    ) -> List[str]:
        """Generate the summary section of the report."""
        content = [
            "## Summary",
            ""
        ]
        
        # Determine contract path
        contract_path = None
        if fuzz_results:
            contract_path = fuzz_results.contract_path
        elif invariant_results:
            contract_path = invariant_results.contract_path
        
        if contract_path:
            content.append(f"**Contract:** `{contract_path}`")
            content.append("")
        
        # Add fuzzing summary
        if fuzz_results:
            content.append("### Fuzzing Results")
            content.append("")
            content.append(f"- **Total Test Pools:** {fuzz_results.total_pools}")
            content.append(f"- **Passing Pools:** {len(fuzz_results.passed_pools)}")
            content.append(f"- **Failing Pools:** {len(fuzz_results.failed_pools)}")
            content.append(f"- **Error Pools:** {len(fuzz_results.error_pools)}")
            content.append(f"- **Success Rate:** {fuzz_results.success_rate:.2%}")
            content.append(f"- **Execution Time:** {fuzz_results.execution_time:.2f} seconds")
            content.append("")
        
        # Add invariant summary
        if invariant_results:
            content.append("### Invariant Verification Results")
            content.append("")
            content.append(f"- **Total Invariants:** {invariant_results.total_invariants}")
            content.append(f"- **Verified Invariants:** {len(invariant_results.verified_invariants)}")
            content.append(f"- **Violated Invariants:** {len(invariant_results.violations)}")
            content.append(f"- **Error Invariants:** {len(invariant_results.errors)}")
            content.append(f"- **Verification Rate:** {invariant_results.verification_rate:.2%}")
            content.append(f"- **Execution Time:** {invariant_results.execution_time:.2f} seconds")
            content.append("")
        
        # Add overall status
        has_issues = (
            (fuzz_results and fuzz_results.has_failures) or
            (invariant_results and invariant_results.has_violations)
        )
        
        if has_issues:
            content.append("### ❌ **ISSUES DETECTED**")
            content.append("")
            
            if fuzz_results and fuzz_results.has_failures:
                content.append(f"- Found {len(fuzz_results.failed_pools)} failing test pools")
            
            if invariant_results and invariant_results.has_violations:
                content.append(f"- Found {len(invariant_results.violations)} invariant violations")
            
            content.append("")
        else:
            content.append("### ✅ **NO ISSUES DETECTED**")
            content.append("")
        
        return content
    
    def _generate_fuzzing_section(self, fuzz_results: FuzzResults) -> List[str]:
        """Generate the fuzzing results section of the report."""
        content = [
            "## Fuzzing Analysis",
            ""
        ]
        
        if not fuzz_results.has_failures:
            content.append("✅ No issues detected in fuzzing analysis.")
            content.append("")
            return content
        
        # Add failing pools
        if fuzz_results.failed_pools:
            content.append("### Failing Test Pools")
            content.append("")
            content.append("| Pool ID | Parameters | Error |")
            content.append("|---------|------------|-------|")
            
            for pool in fuzz_results.failed_pools:
                params = dict(zip(pool.parameter_names, pool.parameters))
                params_str = ", ".join([f"`{k}={v}`" for k, v in params.items()])
                error = pool.error_message or "Unknown error"
                error = error.replace("\n", " ").strip()
                if len(error) > 100:
                    error = error[:97] + "..."
                
                content.append(f"| {pool.id} | {params_str} | {error} |")
            
            content.append("")
        
        # Add error pools
        if fuzz_results.error_pools:
            content.append("### Error Test Pools")
            content.append("")
            content.append("| Pool ID | Parameters | Error |")
            content.append("|---------|------------|-------|")
            
            for pool in fuzz_results.error_pools:
                params = dict(zip(pool.parameter_names, pool.parameters))
                params_str = ", ".join([f"`{k}={v}`" for k, v in params.items()])
                error = pool.error_message or "Unknown error"
                error = error.replace("\n", " ").strip()
                if len(error) > 100:
                    error = error[:97] + "..."
                
                content.append(f"| {pool.id} | {params_str} | {error} |")
            
            content.append("")
        
        # Add minimal failing parameter sets
        if fuzz_results.failed_pools:
            content.append("### Minimal Failing Parameter Sets")
            content.append("")
            content.append("The following parameter combinations are likely causing failures:")
            content.append("")
            
            # Group by parameter name and value
            param_failures = {}
            for pool in fuzz_results.failed_pools:
                for name, value in zip(pool.parameter_names, pool.parameters):
                    key = f"{name}={value}"
                    param_failures[key] = param_failures.get(key, 0) + 1
            
            # Sort by frequency
            sorted_params = sorted(param_failures.items(), key=lambda x: x[1], reverse=True)
            
            content.append("| Parameter | Failure Count |")
            content.append("|-----------|--------------|")
            
            for param, count in sorted_params[:10]:  # Show top 10
                content.append(f"| `{param}` | {count} |")
            
            content.append("")
        
        return content
    
    def _generate_invariant_section(self, invariant_results: InvariantResults) -> List[str]:
        """Generate the invariant verification section of the report."""
        content = [
            "## Invariant Verification",
            ""
        ]
        
        if not invariant_results.has_violations:
            content.append("✅ All invariants verified successfully.")
            content.append("")
            return content
        
        # Add violated invariants
        content.append("### Violated Invariants")
        content.append("")
        
        for i, inv in enumerate(invariant_results.violations):
            content.append(f"#### {i+1}. {inv.function_name} - {inv.type}")
            content.append("")
            content.append(f"**Description:** {inv.description}")
            content.append("")
            content.append("**Solidity Assertion:**")
            content.append("```solidity")
            content.append(inv.solidity_assertion)
            content.append("```")
            content.append("")
            
            if inv.counterexample:
                content.append("**Counterexample:**")
                content.append("```")
                for var, value in inv.counterexample.items():
                    content.append(f"{var} = {value}")
                content.append("```")
                content.append("")
        
        return content
    
    def _generate_recommendations(
        self,
        fuzz_results: Optional[FuzzResults],
        invariant_results: Optional[InvariantResults]
    ) -> List[str]:
        """Generate recommendations based on analysis results."""
        content = [
            "## Recommendations",
            ""
        ]
        
        has_issues = (
            (fuzz_results and fuzz_results.has_failures) or
            (invariant_results and invariant_results.has_violations)
        )
        
        if not has_issues:
            content.append("✅ No issues detected. The contract appears to be functioning correctly.")
            content.append("")
            return content
        
        # Add general recommendations
        content.append("Based on the analysis, consider the following recommendations:")
        content.append("")
        
        # Fuzzing recommendations
        if fuzz_results and fuzz_results.has_failures:
            content.append("### Fuzzing Recommendations")
            content.append("")
            content.append("1. **Review Input Validation:** Add proper input validation for function parameters.")
            content.append("2. **Check Edge Cases:** Pay special attention to edge cases like zero values, maximum integers, etc.")
            content.append("3. **Add Explicit Require Statements:** Make preconditions explicit with require statements.")
            content.append("")
        
        # Invariant recommendations
        if invariant_results and invariant_results.has_violations:
            content.append("### Invariant Recommendations")
            content.append("")
            content.append("1. **Fix Violated Invariants:** Address each violated invariant listed above.")
            content.append("2. **Add Formal Verification:** Consider adding formal verification to critical functions.")
            content.append("3. **Strengthen Preconditions:** Add more restrictive preconditions to prevent invalid states.")
            content.append("")
        
        return content
