// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_getBalance_0 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_getBalance_pool_0() public {
        // Test pool 0: {'user': '0x0000000000000000000000000000000000000000'}
        try target.getBalance(address(0x0000000000000000000000000000000000000000)) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}