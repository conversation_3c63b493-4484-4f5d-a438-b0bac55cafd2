// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_calculateInterest_17 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_calculateInterest_pool_17() public {
        // Test pool 17: {'principal': 0, 'rate': 1, 'time': 365}
        try target.calculateInterest(0, 1, 365) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}