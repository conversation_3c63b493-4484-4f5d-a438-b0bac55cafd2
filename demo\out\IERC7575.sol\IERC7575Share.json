{"abi": [{"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceID", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "vault", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "VaultUpdate", "inputs": [{"name": "asset", "type": "address", "indexed": true, "internalType": "address"}, {"name": "vault", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"supportsInterface(bytes4)": "01ffc9a7", "vault(address)": "f815c03d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"VaultUpdate\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceID\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"vault\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Interface of the ERC20 share token, as defined in https://eips.ethereum.org/EIPS/eip-7575\",\"kind\":\"dev\",\"methods\":{\"supportsInterface(bytes4)\":{\"details\":\"Interface identification is specified in ERC-165. This function uses less than 30,000 gas.\",\"params\":{\"interfaceID\":\"The interface identifier, as specified in ERC-165\"},\"returns\":{\"_0\":\"`true` if the contract implements `interfaceID` and `interfaceID` is not 0xffffffff, `false` otherwise\"}},\"vault(address)\":{\"details\":\"Returns the address of the Vault for the given asset.\",\"params\":{\"asset\":\"the ERC-20 token to deposit with into the Vault\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"supportsInterface(bytes4)\":{\"notice\":\"Query if a contract implements an interface\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/interfaces/IERC7575.sol\":\"IERC7575Share\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/interfaces/IERC165.sol\":{\"keccak256\":\"0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc\",\"dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT\"]},\"lib/forge-std/src/interfaces/IERC7575.sol\":{\"keccak256\":\"0xb46aebdd749e632cf76466d2f75428f8e41e8283145818b621acd2624793782c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b32be8816d4658db66fdca29e9a9bc3a071bc47da19d6afa950eec5a32781ccb\",\"dweb:/ipfs/QmbJ27FAJz4rxV2xTzEPHkihYRrvhHH9mdkNSdxvba5Zxf\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "asset", "type": "address", "indexed": true}, {"internalType": "address", "name": "vault", "type": "address", "indexed": false}], "type": "event", "name": "VaultUpdate", "anonymous": false}, {"inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "vault", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"supportsInterface(bytes4)": {"details": "Interface identification is specified in ERC-165. This function uses less than 30,000 gas.", "params": {"interfaceID": "The interface identifier, as specified in ERC-165"}, "returns": {"_0": "`true` if the contract implements `interfaceID` and `interfaceID` is not 0xffffffff, `false` otherwise"}}, "vault(address)": {"details": "Returns the address of the Vault for the given asset.", "params": {"asset": "the ERC-20 token to deposit with into the Vault"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"supportsInterface(bytes4)": {"notice": "Query if a contract implements an interface"}}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/interfaces/IERC7575.sol": "IERC7575Share"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"lib/forge-std/src/interfaces/IERC165.sol": {"keccak256": "0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600", "urls": ["bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc", "dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT"], "license": "MIT"}, "lib/forge-std/src/interfaces/IERC7575.sol": {"keccak256": "0xb46aebdd749e632cf76466d2f75428f8e41e8283145818b621acd2624793782c", "urls": ["bzz-raw://b32be8816d4658db66fdca29e9a9bc3a071bc47da19d6afa950eec5a32781ccb", "dweb:/ipfs/QmbJ27FAJz4rxV2xTzEPHkihYRrvhHH9mdkNSdxvba5Zxf"], "license": "MIT"}}, "version": 1}, "id": 26}