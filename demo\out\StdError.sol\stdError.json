{"abi": [{"type": "function", "name": "arithmeticError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "assertionError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "divisionError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "encodeStorageError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "enumConversionError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "indexOOBError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "memOverflowError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "popError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "zeroVarError", "inputs": [], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}], "bytecode": {"object": "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", "sourceMap": "162:850:7:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "162:850:7:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;740:85;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;185:86;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;461:91;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;277:87;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;831:88;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;654:80;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;925:84;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;558:90;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;370:85;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;740;820:4;778:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;740:85;:::o;185:86::-;266:4;224:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;185:86;:::o;461:91::-;547:4;505:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;461:91;:::o;277:87::-;359:4;317:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;277:87;:::o;831:88::-;914:4;872:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;831:88;:::o;654:80::-;729:4;687:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;654:80;:::o;925:84::-;1004:4;962:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;925:84;:::o;558:90::-;643:4;601:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;558:90;:::o;370:85::-;450:4;408:47;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;370:85;:::o;7:98:49:-;58:6;92:5;86:12;76:22;;7:98;;;:::o;111:176::-;202:11;236:6;231:3;224:19;276:4;271:3;267:14;252:29;;111:176;;;;:::o;293:246::-;374:1;384:113;398:6;395:1;392:13;384:113;;;483:1;478:3;474:11;468:18;464:1;459:3;455:11;448:39;420:2;417:1;413:10;408:15;;384:113;;;531:1;522:6;517:3;513:16;506:27;355:184;293:246;;;:::o;545:102::-;586:6;637:2;633:7;628:2;621:5;617:14;613:28;603:38;;545:102;;;:::o;653:389::-;747:3;775:38;807:5;775:38;:::i;:::-;829:78;900:6;895:3;829:78;:::i;:::-;822:85;;916:65;974:6;969:3;962:4;955:5;951:16;916:65;:::i;:::-;1006:29;1028:6;1006:29;:::i;:::-;1001:3;997:39;990:46;;751:291;653:389;;;;:::o;1048:325::-;1167:4;1205:2;1194:9;1190:18;1182:26;;1254:9;1248:4;1244:20;1240:1;1229:9;1225:17;1218:47;1282:84;1361:4;1352:6;1282:84;:::i;:::-;1274:92;;1048:325;;;;:::o;1379:86::-;1425:7;1454:5;1443:16;;1379:86;;;:::o;1471:::-;1506:7;1546:4;1539:5;1535:16;1524:27;;1471:86;;;:::o;1563:60::-;1591:3;1612:5;1605:12;;1563:60;;;:::o;1629:156::-;1686:9;1719:60;1735:43;1744:33;1771:5;1744:33;:::i;:::-;1735:43;:::i;:::-;1719:60;:::i;:::-;1706:73;;1629:156;;;:::o;1791:145::-;1885:44;1923:5;1885:44;:::i;:::-;1880:3;1873:57;1791:145;;:::o;1942:236::-;2042:4;2080:2;2069:9;2065:18;2057:26;;2093:78;2168:1;2157:9;2153:17;2144:6;2093:78;:::i;:::-;1942:236;;;;:::o;2184:85::-;2229:7;2258:5;2247:16;;2184:85;;;:::o;2275:154::-;2331:9;2364:59;2380:42;2389:32;2415:5;2389:32;:::i;:::-;2380:42;:::i;:::-;2364:59;:::i;:::-;2351:72;;2275:154;;;:::o;2435:143::-;2528:43;2565:5;2528:43;:::i;:::-;2523:3;2516:56;2435:143;;:::o;2584:234::-;2683:4;2721:2;2710:9;2706:18;2698:26;;2734:77;2808:1;2797:9;2793:17;2784:6;2734:77;:::i;:::-;2584:234;;;;:::o;2824:86::-;2870:7;2899:5;2888:16;;2824:86;;;:::o;2916:156::-;2973:9;3006:60;3022:43;3031:33;3058:5;3031:33;:::i;:::-;3022:43;:::i;:::-;3006:60;:::i;:::-;2993:73;;2916:156;;;:::o;3078:145::-;3172:44;3210:5;3172:44;:::i;:::-;3167:3;3160:57;3078:145;;:::o;3229:236::-;3329:4;3367:2;3356:9;3352:18;3344:26;;3380:78;3455:1;3444:9;3440:17;3431:6;3380:78;:::i;:::-;3229:236;;;;:::o;3471:86::-;3517:7;3546:5;3535:16;;3471:86;;;:::o;3563:156::-;3620:9;3653:60;3669:43;3678:33;3705:5;3678:33;:::i;:::-;3669:43;:::i;:::-;3653:60;:::i;:::-;3640:73;;3563:156;;;:::o;3725:145::-;3819:44;3857:5;3819:44;:::i;:::-;3814:3;3807:57;3725:145;;:::o;3876:236::-;3976:4;4014:2;4003:9;3999:18;3991:26;;4027:78;4102:1;4091:9;4087:17;4078:6;4027:78;:::i;:::-;3876:236;;;;:::o;4118:86::-;4164:7;4193:5;4182:16;;4118:86;;;:::o;4210:156::-;4267:9;4300:60;4316:43;4325:33;4352:5;4325:33;:::i;:::-;4316:43;:::i;:::-;4300:60;:::i;:::-;4287:73;;4210:156;;;:::o;4372:145::-;4466:44;4504:5;4466:44;:::i;:::-;4461:3;4454:57;4372:145;;:::o;4523:236::-;4623:4;4661:2;4650:9;4646:18;4638:26;;4674:78;4749:1;4738:9;4734:17;4725:6;4674:78;:::i;:::-;4523:236;;;;:::o;4765:86::-;4811:7;4840:5;4829:16;;4765:86;;;:::o;4857:156::-;4914:9;4947:60;4963:43;4972:33;4999:5;4972:33;:::i;:::-;4963:43;:::i;:::-;4947:60;:::i;:::-;4934:73;;4857:156;;;:::o;5019:145::-;5113:44;5151:5;5113:44;:::i;:::-;5108:3;5101:57;5019:145;;:::o;5170:236::-;5270:4;5308:2;5297:9;5293:18;5285:26;;5321:78;5396:1;5385:9;5381:17;5372:6;5321:78;:::i;:::-;5170:236;;;;:::o;5412:86::-;5458:7;5487:5;5476:16;;5412:86;;;:::o;5504:156::-;5561:9;5594:60;5610:43;5619:33;5646:5;5619:33;:::i;:::-;5610:43;:::i;:::-;5594:60;:::i;:::-;5581:73;;5504:156;;;:::o;5666:145::-;5760:44;5798:5;5760:44;:::i;:::-;5755:3;5748:57;5666:145;;:::o;5817:236::-;5917:4;5955:2;5944:9;5940:18;5932:26;;5968:78;6043:1;6032:9;6028:17;6019:6;5968:78;:::i;:::-;5817:236;;;;:::o;6059:86::-;6105:7;6134:5;6123:16;;6059:86;;;:::o;6151:156::-;6208:9;6241:60;6257:43;6266:33;6293:5;6266:33;:::i;:::-;6257:43;:::i;:::-;6241:60;:::i;:::-;6228:73;;6151:156;;;:::o;6313:145::-;6407:44;6445:5;6407:44;:::i;:::-;6402:3;6395:57;6313:145;;:::o;6464:236::-;6564:4;6602:2;6591:9;6587:18;6579:26;;6615:78;6690:1;6679:9;6675:17;6666:6;6615:78;:::i;:::-;6464:236;;;;:::o;6706:86::-;6752:7;6781:5;6770:16;;6706:86;;;:::o;6798:156::-;6855:9;6888:60;6904:43;6913:33;6940:5;6913:33;:::i;:::-;6904:43;:::i;:::-;6888:60;:::i;:::-;6875:73;;6798:156;;;:::o;6960:145::-;7054:44;7092:5;7054:44;:::i;:::-;7049:3;7042:57;6960:145;;:::o;7111:236::-;7211:4;7249:2;7238:9;7234:18;7226:26;;7262:78;7337:1;7326:9;7322:17;7313:6;7262:78;:::i;:::-;7111:236;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"arithmeticError()": "8995290f", "assertionError()": "10332977", "divisionError()": "fa784a44", "encodeStorageError()": "d160e4de", "enumConversionError()": "1de45560", "indexOOBError()": "05ee8612", "memOverflowError()": "986c5f68", "popError()": "b22dc54d", "zeroVarError()": "b67689da"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"arithmeticError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"assertionError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"divisionError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"encodeStorageError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"enumConversionError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"indexOOBError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"memOverflowError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"popError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"zeroVarError\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdError.sol\":\"stdError\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "arithmeticError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "assertionError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "divisionError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "encodeStorageError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "enumConversionError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "indexOOBError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "memOverflowError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "popError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "zeroVarError", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdError.sol": "stdError"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}}, "version": 1}, "id": 7}