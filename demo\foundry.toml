[profile.default]
src = "."
out = "out"
libs = ["lib"]
test = "test"

# See more config options https://github.com/foundry-rs/foundry/tree/master/crates/config

[profile.default.fuzz]
runs = 256
max_test_rejects = 65536
seed = '0x3e8'
dictionary_weight = 40
include_storage = true
include_push_bytes = true

[profile.default.invariant]
runs = 256
depth = 15
fail_on_revert = false
call_override = false
dictionary_weight = 80
include_storage = true
include_push_bytes = true

[rpc_endpoints]
mainnet = "https://eth-mainnet.alchemyapi.io/v2/${ALCHEMY_API_KEY}"
goerli = "https://eth-goerli.alchemyapi.io/v2/${ALCHEMY_API_KEY}"
