"""
Contract analysis using Slither to extract function signatures and metadata.
"""

import json
import subprocess
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional
from rich.console import Console

from .models import ContractAnalysis, FunctionSignature

console = Console()


class ContractAnalyzer:
    """Analyzes Solidity contracts using Slither."""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
    
    def analyze_contract(self, contract_path: Path) -> ContractAnalysis:
        """Analyze a Solidity contract to extract function signatures."""
        if self.verbose:
            console.print(f"🔍 Analyzing contract: {contract_path}")
        
        analysis = ContractAnalysis(
            contract_path=contract_path,
            contract_name=self._extract_contract_name(contract_path)
        )
        
        try:
            # Run Slither analysis
            slither_output = self._run_slither_analysis(contract_path)
            
            if slither_output:
                # Parse Slither output to extract functions
                analysis.functions = self._parse_slither_output(slither_output)
            else:
                # Fallback: try to parse contract manually
                analysis.functions = self._parse_contract_manually(contract_path)
            
        except Exception as e:
            analysis.analysis_errors.append(f"Analysis failed: {str(e)}")
            if self.verbose:
                console.print(f"❌ Analysis error: {e}")
        
        if self.verbose:
            console.print(f"✅ Found {len(analysis.functions)} functions")
        
        return analysis
    
    def _extract_contract_name(self, contract_path: Path) -> str:
        """Extract contract name from file path or content."""
        if contract_path.is_file():
            # Try to extract from filename first
            name = contract_path.stem
            if name.endswith(".sol"):
                name = name[:-4]
            return name
        else:
            # For directories, look for main contract
            sol_files = list(contract_path.glob("*.sol"))
            if sol_files:
                return sol_files[0].stem
            return "Contract"
    
    def _run_slither_analysis(self, contract_path: Path) -> Optional[Dict[str, Any]]:
        """Run Slither analysis and return parsed JSON output."""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
                tmp_path = Path(tmp_file.name)
            
            cmd = [
                "slither",
                str(contract_path),
                "--print", "contract-summary",
                "--json", str(tmp_path)
            ]
            
            if self.verbose:
                console.print(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0 and tmp_path.exists():
                with open(tmp_path) as f:
                    data = json.load(f)
                tmp_path.unlink()  # Clean up
                return data
            else:
                if self.verbose:
                    console.print(f"Slither failed: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            if self.verbose:
                console.print("Slither analysis timed out")
            return None
        except Exception as e:
            if self.verbose:
                console.print(f"Slither execution failed: {e}")
            return None
    
    def _parse_slither_output(self, slither_data: Dict[str, Any]) -> List[FunctionSignature]:
        """Parse Slither JSON output to extract function signatures."""
        functions = []
        
        try:
            # Navigate Slither's JSON structure
            if "results" in slither_data and "detectors" in slither_data["results"]:
                # This is detector output, not what we want
                return []
            
            # Look for contract information
            if "results" in slither_data:
                for result in slither_data["results"]:
                    if "elements" in result:
                        for element in result["elements"]:
                            if element.get("type") == "function":
                                func_sig = self._parse_function_element(element)
                                if func_sig:
                                    functions.append(func_sig)
            
        except Exception as e:
            if self.verbose:
                console.print(f"Error parsing Slither output: {e}")
        
        return functions
    
    def _parse_function_element(self, element: Dict[str, Any]) -> Optional[FunctionSignature]:
        """Parse a function element from Slither output."""
        try:
            name = element.get("name", "")
            visibility = element.get("visibility", "public")
            state_mutability = element.get("stateMutability", "nonpayable")
            
            # Parse parameters
            inputs = []
            if "parameters" in element:
                for param in element["parameters"]:
                    inputs.append({
                        "name": param.get("name", ""),
                        "type": param.get("type", "")
                    })
            
            # Parse return values
            outputs = []
            if "returnParameters" in element:
                for param in element["returnParameters"]:
                    outputs.append({
                        "name": param.get("name", ""),
                        "type": param.get("type", "")
                    })
            
            return FunctionSignature(
                name=name,
                inputs=inputs,
                outputs=outputs,
                visibility=visibility,
                stateMutability=state_mutability
            )
            
        except Exception as e:
            if self.verbose:
                console.print(f"Error parsing function element: {e}")
            return None
    
    def _parse_contract_manually(self, contract_path: Path) -> List[FunctionSignature]:
        """Fallback: manually parse contract file for function signatures."""
        functions = []
        
        try:
            if contract_path.is_file():
                content = contract_path.read_text()
            else:
                # For directories, find and read .sol files
                sol_files = list(contract_path.glob("**/*.sol"))
                if not sol_files:
                    return []
                content = sol_files[0].read_text()
            
            # Simple regex-based parsing (basic implementation)
            import re
            
            # Match function declarations
            function_pattern = r'function\s+(\w+)\s*\((.*?)\)\s*(public|external|internal|private)?\s*(view|pure|payable)?\s*(returns\s*\((.*?)\))?'
            
            for match in re.finditer(function_pattern, content, re.MULTILINE | re.DOTALL):
                name = match.group(1)
                params_str = match.group(2).strip()
                visibility = match.group(3) or "public"
                state_mutability = match.group(4) or "nonpayable"
                returns_str = match.group(6) or ""
                
                # Parse parameters
                inputs = self._parse_parameters(params_str)
                outputs = self._parse_parameters(returns_str)
                
                if visibility in ["public", "external"]:  # Only include public/external functions
                    functions.append(FunctionSignature(
                        name=name,
                        inputs=inputs,
                        outputs=outputs,
                        visibility=visibility,
                        stateMutability=state_mutability
                    ))
            
        except Exception as e:
            if self.verbose:
                console.print(f"Manual parsing failed: {e}")
        
        return functions
    
    def _parse_parameters(self, params_str: str) -> List[Dict[str, str]]:
        """Parse parameter string into list of parameter dictionaries."""
        if not params_str.strip():
            return []
        
        params = []
        # Simple parsing - split by comma and extract type/name
        for param in params_str.split(','):
            param = param.strip()
            if param:
                parts = param.split()
                if len(parts) >= 2:
                    param_type = parts[0]
                    param_name = parts[1]
                    params.append({"name": param_name, "type": param_type})
                elif len(parts) == 1:
                    # Only type, no name
                    params.append({"name": f"param{len(params)}", "type": parts[0]})
        
        return params
