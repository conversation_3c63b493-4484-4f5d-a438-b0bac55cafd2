// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_calculateInterest_10 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_calculateInterest_pool_10() public {
        // Test pool 10: {'principal': 1000, 'rate': 1, 'time': 1}
        try target.calculateInterest(1000, 1, 1) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}