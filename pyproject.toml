[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "smart-bug-hunter"
version = "0.1.0"
description = "Combinatorial fuzzing and formal verification tool for Solidity contracts"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "Smart Bug Hunter Team", email = "<EMAIL>"}
]
keywords = ["solidity", "fuzzing", "formal-verification", "smart-contracts", "security"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Topic :: Security",
    "Topic :: Software Development :: Testing",
]

dependencies = [
    "typer[all]>=0.9.0",
    "allpairspy>=2.5.0",
    "openai>=1.0.0",
    "z3-solver>=4.12.0",
    "slither-analyzer>=0.10.0",
    "rich>=13.0.0",
    "pydantic>=2.0.0",
    "requests>=2.31.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

[project.scripts]
smart-bug-hunter = "smart_bug_hunter.cli:app"

[project.urls]
Homepage = "https://github.com/smart-bug-hunter/smart-bug-hunter"
Repository = "https://github.com/smart-bug-hunter/smart-bug-hunter"
Issues = "https://github.com/smart-bug-hunter/smart-bug-hunter/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["smart_bug_hunter*"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
