name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .
        pip install pytest pytest-cov black isort mypy
    
    - name: Install Foundry
      uses: foundry-rs/foundry-toolchain@v1
      with:
        version: nightly
    
    - name: Lint with black
      run: |
        black --check .
    
    - name: Lint with isort
      run: |
        isort --check .
    
    - name: Type check with mypy
      run: |
        mypy smart_bug_hunter
    
    - name: Test with pytest
      run: |
        pytest --cov=smart_bug_hunter
    
    - name: Run demo
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      run: |
        cd demo
        chmod +x run_demo.sh
        ./run_demo.sh
    
    - name: Upload report
      uses: actions/upload-artifact@v3
      with:
        name: smart-bug-hunter-report
        path: demo/output/smart_bug_hunter_report.md
        if-no-files-found: warn

  build:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
    
    - name: Build package
      run: |
        python -m build
    
    - name: Check package
      run: |
        twine check dist/*
    
    - name: Upload package
      uses: actions/upload-artifact@v3
      with:
        name: smart-bug-hunter-package
        path: dist/
        if-no-files-found: error

  # Uncomment to enable automatic PyPI publishing on release
  # publish:
  #   runs-on: ubuntu-latest
  #   needs: build
  #   if: startsWith(github.ref, 'refs/tags/')
  #   
  #   steps:
  #   - uses: actions/download-artifact@v3
  #     with:
  #       name: smart-bug-hunter-package
  #       path: dist/
  #   
  #   - name: Publish to PyPI
  #     uses: pypa/gh-action-pypi-publish@release/v1
  #     with:
  #       user: __token__
  #       password: ${{ secrets.PYPI_API_TOKEN }}
