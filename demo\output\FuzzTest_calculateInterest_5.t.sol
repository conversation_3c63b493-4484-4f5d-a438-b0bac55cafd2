// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_calculateInterest_5 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_calculateInterest_pool_5() public {
        // Test pool 5: {'principal': 1000, 'rate': 1000, 'time': 30}
        try target.calculateInterest(1000, 1000, 30) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}