// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_deposit_1 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_deposit_pool_1() public {
        // Test pool 1: {'amount': 1}
        try target.deposit(1) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}