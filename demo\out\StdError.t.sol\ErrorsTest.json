{"abi": [{"type": "function", "name": "arithmeticError", "inputs": [{"name": "a", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertionError", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "divError", "inputs": [{"name": "a", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "encodeStgError", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enumConversion", "inputs": [{"name": "a", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "indexOOBError", "inputs": [{"name": "a", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "intern", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "mem", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "modError", "inputs": [{"name": "a", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "pop", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "someArr", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}], "bytecode": {"object": "0x608060405234801561000f575f80fd5b506107368061001d5f395ff3fe608060405234801561000f575f80fd5b50600436106100a7575f3560e01c8063a4ece52c1161006f578063a4ece52c14610117578063b072347d14610121578063bb0bfc251461013d578063d452309914610159578063d810d9ed14610175578063e039355514610191576100a7565b806303ed6f22146100ab5780630f1031ec146100db57806310332977146100f957806385883d8c1461010357806395736c001461010d575b5f80fd5b6100c560048036038101906100c091906103a3565b6101ad565b6040516100d291906103dd565b60405180910390f35b6100e36101cc565b6040516100f091906103dd565b60405180910390f35b6101016101e7565b005b61010b6101f7565b005b610115610268565b005b61011f610286565b005b61013b600480360381019061013691906103a3565b6102ac565b005b610157600480360381019061015291906103a3565b610318565b005b610173600480360381019061016e91906103a3565b61032e565b005b61018f600480360381019061018a91906103a3565b61033f565b005b6101ab60048036038101906101a691906103a3565b610350565b005b5f81815481106101bb575f80fd5b905f5260205f20015f915090505481565b5f6103626101de60028263ffffffff16565b50600791505090565b5f6101f5576101f46103f6565b5b565b5f7f080000000000000000000000000000000000000000000000000000000000000090508067ffffffffffffffff81111561023557610234610423565b5b6040519080825280602002602001820160405280156102635781602001602082028036833780820191505090505b505050565b60018055600160405161027b9190610549565b604051809103902050565b5f8054806102975761029661055f565b5b600190038181905f5260205f20015f90559055565b5f8067ffffffffffffffff8111156102c7576102c6610423565b5b6040519080825280602002602001820160405280156102f55781602001602082028036833780820191505090505b50905080828151811061030b5761030a61058c565b5b6020026020010151505050565b805f81111561032a576103296105b9565b5b5050565b80606461033b9190610613565b5050565b80606461034c9190610670565b5050565b60648161035d91906106a0565b905050565b61036a6106d3565b565b5f80fd5b5f819050919050565b61038281610370565b811461038c575f80fd5b50565b5f8135905061039d81610379565b92915050565b5f602082840312156103b8576103b761036c565b5b5f6103c58482850161038f565b91505092915050565b6103d781610370565b82525050565b5f6020820190506103f05f8301846103ce565b92915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52600160045260245ffd5b7f4e487b71000000000000000000000000000000000000000000000000000000005f52604160045260245ffd5b7f4e487b71000000000000000000000000000000000000000000000000000000005f52602260045260245ffd5b5f600282049050600182168061049457607f821691505b6020821081036104a7576104a6610450565b5b50919050565b5f81905092915050565b5f819050815f5260205f209050919050565b5f81546104d58161047d565b6104df81866104ad565b9450600182165f81146104f9576001811461050e57610540565b60ff1983168652811515820286019350610540565b610517856104b7565b5f5b8381101561053857815481890152600182019150602081019050610519565b838801955050505b50505092915050565b5f61055482846104c9565b915081905092915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52603160045260245ffd5b7f4e487b71000000000000000000000000000000000000000000000000000000005f52603260045260245ffd5b7f4e487b71000000000000000000000000000000000000000000000000000000005f52602160045260245ffd5b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601260045260245ffd5b5f61061d82610370565b915061062883610370565b925082610638576106376105e6565b5b828206905092915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f61067a82610370565b915061068583610370565b925082610695576106946105e6565b5b828204905092915050565b5f6106aa82610370565b91506106b583610370565b92508282039050818111156106cd576106cc610643565b5b92915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52605160045260245ffdfea26469706673582212209d9c21204aa39a7f89ac24afb01e5d210c4c8e501aded5de89a504f33bff2ab864736f6c63430008170033", "sourceMap": "1643:1083:34:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1643:1083:34:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1700:24;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2586:138;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;1752:68;;;:::i;:::-;;2485:95;;;:::i;:::-;;2121:180;;;:::i;:::-;;2307:52;;;:::i;:::-;;2365:114;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2047:68;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1976:65;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1905;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1826:73;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;1700:24;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;2586:138::-;2620:7;2639:46;2695:4;2697:1;2695;:4;;:::i;:::-;;2716:1;2709:8;;;2586:138;:::o;1752:68::-;1807:5;1800:13;;;;:::i;:::-;;1752:68::o;2485:95::-;2522:9;2534:13;2522:25;;2571:1;2557:16;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2512:68;2485:95::o;2121:180::-;2253:1;2237:14;2230:25;2284:9;2274:20;;;;;;:::i;:::-;;;;;;;;;2121:180::o;2307:52::-;2339:7;:13;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;2307:52::o;2365:114::-;2421:18;2456:1;2442:16;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2421:37;;2468:1;2470;2468:4;;;;;;;;:::i;:::-;;;;;;;;;2411:68;2365:114;:::o;2047:68::-;2106:1;2104:4;;;;;;;;:::i;:::-;;;2047:68;:::o;1976:65::-;2033:1;2027:3;:7;;;;:::i;:::-;;1976:65;:::o;1905:::-;1962:1;1956:3;:7;;;;:::i;:::-;;1905:65;:::o;1826:73::-;1889:3;1884:8;;;;;:::i;:::-;;;1826:73;:::o;-1:-1:-1:-;;;:::i;:::-;:::o;88:117:49:-;197:1;194;187:12;334:77;371:7;400:5;389:16;;334:77;;;:::o;417:122::-;490:24;508:5;490:24;:::i;:::-;483:5;480:35;470:63;;529:1;526;519:12;470:63;417:122;:::o;545:139::-;591:5;629:6;616:20;607:29;;645:33;672:5;645:33;:::i;:::-;545:139;;;;:::o;690:329::-;749:6;798:2;786:9;777:7;773:23;769:32;766:119;;;804:79;;:::i;:::-;766:119;924:1;949:53;994:7;985:6;974:9;970:22;949:53;:::i;:::-;939:63;;895:117;690:329;;;;:::o;1025:118::-;1112:24;1130:5;1112:24;:::i;:::-;1107:3;1100:37;1025:118;;:::o;1149:222::-;1242:4;1280:2;1269:9;1265:18;1257:26;;1293:71;1361:1;1350:9;1346:17;1337:6;1293:71;:::i;:::-;1149:222;;;;:::o;1377:180::-;1425:77;1422:1;1415:88;1522:4;1519:1;1512:15;1546:4;1543:1;1536:15;1563:180;1611:77;1608:1;1601:88;1708:4;1705:1;1698:15;1732:4;1729:1;1722:15;1749:180;1797:77;1794:1;1787:88;1894:4;1891:1;1884:15;1918:4;1915:1;1908:15;1935:320;1979:6;2016:1;2010:4;2006:12;1996:22;;2063:1;2057:4;2053:12;2084:18;2074:81;;2140:4;2132:6;2128:17;2118:27;;2074:81;2202:2;2194:6;2191:14;2171:18;2168:38;2165:84;;2221:18;;:::i;:::-;2165:84;1986:269;1935:320;;;:::o;2261:147::-;2362:11;2399:3;2384:18;;2261:147;;;;:::o;2414:140::-;2462:4;2485:3;2477:11;;2508:3;2505:1;2498:14;2542:4;2539:1;2529:18;2521:26;;2414:140;;;:::o;2582:870::-;2683:3;2720:5;2714:12;2749:36;2775:9;2749:36;:::i;:::-;2801:88;2882:6;2877:3;2801:88;:::i;:::-;2794:95;;2920:1;2909:9;2905:17;2936:1;2931:166;;;;3111:1;3106:340;;;;2898:548;;2931:166;3015:4;3011:9;3000;2996:25;2991:3;2984:38;3077:6;3070:14;3063:22;3055:6;3051:35;3046:3;3042:45;3035:52;;2931:166;;3106:340;3173:37;3204:5;3173:37;:::i;:::-;3232:1;3246:154;3260:6;3257:1;3254:13;3246:154;;;3334:7;3328:14;3324:1;3319:3;3315:11;3308:35;3384:1;3375:7;3371:15;3360:26;;3282:4;3279:1;3275:12;3270:17;;3246:154;;;3429:6;3424:3;3420:16;3413:23;;3113:333;;2898:548;;2687:765;;2582:870;;;;:::o;3458:265::-;3585:3;3607:90;3693:3;3684:6;3607:90;:::i;:::-;3600:97;;3714:3;3707:10;;3458:265;;;;:::o;3729:180::-;3777:77;3774:1;3767:88;3874:4;3871:1;3864:15;3898:4;3895:1;3888:15;3915:180;3963:77;3960:1;3953:88;4060:4;4057:1;4050:15;4084:4;4081:1;4074:15;4101:180;4149:77;4146:1;4139:88;4246:4;4243:1;4236:15;4270:4;4267:1;4260:15;4287:180;4335:77;4332:1;4325:88;4432:4;4429:1;4422:15;4456:4;4453:1;4446:15;4473:176;4505:1;4522:20;4540:1;4522:20;:::i;:::-;4517:25;;4556:20;4574:1;4556:20;:::i;:::-;4551:25;;4595:1;4585:35;;4600:18;;:::i;:::-;4585:35;4641:1;4638;4634:9;4629:14;;4473:176;;;;:::o;4655:180::-;4703:77;4700:1;4693:88;4800:4;4797:1;4790:15;4824:4;4821:1;4814:15;4841:185;4881:1;4898:20;4916:1;4898:20;:::i;:::-;4893:25;;4932:20;4950:1;4932:20;:::i;:::-;4927:25;;4971:1;4961:35;;4976:18;;:::i;:::-;4961:35;5018:1;5015;5011:9;5006:14;;4841:185;;;;:::o;5032:194::-;5072:4;5092:20;5110:1;5092:20;:::i;:::-;5087:25;;5126:20;5144:1;5126:20;:::i;:::-;5121:25;;5170:1;5167;5163:9;5155:17;;5194:1;5188:4;5185:11;5182:37;;;5199:18;;:::i;:::-;5182:37;5032:194;;;;:::o;5232:180::-;5280:77;5277:1;5270:88;5377:4;5374:1;5367:15;5401:4;5398:1;5391:15", "linkReferences": {}}, "methodIdentifiers": {"arithmeticError(uint256)": "e0393555", "assertionError()": "10332977", "divError(uint256)": "d810d9ed", "encodeStgError()": "95736c00", "enumConversion(uint256)": "bb0bfc25", "indexOOBError(uint256)": "b072347d", "intern()": "0f1031ec", "mem()": "85883d8c", "modError(uint256)": "d4523099", "pop()": "a4ece52c", "someArr(uint256)": "03ed6f22"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"a\",\"type\":\"uint256\"}],\"name\":\"arithmeticError\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"assertionError\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"a\",\"type\":\"uint256\"}],\"name\":\"divError\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"encodeStgError\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"a\",\"type\":\"uint256\"}],\"name\":\"enumConversion\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"a\",\"type\":\"uint256\"}],\"name\":\"indexOOBError\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"intern\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"mem\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"a\",\"type\":\"uint256\"}],\"name\":\"modError\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pop\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"someArr\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/test/StdError.t.sol\":\"ErrorsTest\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x399d0b11c8e4e902b6c95c21c187968e62e1bd2a86b8f6dad882c399a05404c1\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://82618dd9135c30870716bb9561f238b68b0c46cff6c7311a3d4b1c35a541c633\",\"dweb:/ipfs/QmSw3gEwXthgoDAqE3gemC5JfQnBnRBTFGEJaDNBj7JwxH\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/forge-std/test/StdError.t.sol\":{\"keccak256\":\"0xf5197e27fa0f38c4821acd73f270a05dc81be722e5e8e3cc58ce1bb84ab28051\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://231e49b55dbdf967531e738f3a93680fbc988c860363c5ef20ff4071a644241e\",\"dweb:/ipfs/QmbTx9MpTzwtu36zex8Cs9bCnmtpdhPajGCftLzAL752ya\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "arithmeticError"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "assertionError"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "divError"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "encodeStgError"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "enumConversion"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "indexOOBError"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "intern", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "mem"}, {"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "modError"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "pop"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "someArr", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/test/StdError.t.sol": "ErrorsTest"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x399d0b11c8e4e902b6c95c21c187968e62e1bd2a86b8f6dad882c399a05404c1", "urls": ["bzz-raw://82618dd9135c30870716bb9561f238b68b0c46cff6c7311a3d4b1c35a541c633", "dweb:/ipfs/QmSw3gEwXthgoDAqE3gemC5JfQnBnRBTFGEJaDNBj7JwxH"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/forge-std/test/StdError.t.sol": {"keccak256": "0xf5197e27fa0f38c4821acd73f270a05dc81be722e5e8e3cc58ce1bb84ab28051", "urls": ["bzz-raw://231e49b55dbdf967531e738f3a93680fbc988c860363c5ef20ff4071a644241e", "dweb:/ipfs/QmbTx9MpTzwtu36zex8Cs9bCnmtpdhPajGCftLzAL752ya"], "license": "MIT"}}, "version": 1}, "id": 34}