// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_transfer_4 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_transfer_pool_4() public {
        // Test pool 4: {'to': '0x1111111111111111111111111111111111111111', 'amount': 1}
        try target.transfer(address(0x1111111111111111111111111111111111111111), 1) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}