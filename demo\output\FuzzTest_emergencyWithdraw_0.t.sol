// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_emergencyWithdraw_0 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_emergencyWithdraw_pool_0() public {
        // Test pool 0: {'amount': 0}
        try target.emergencyWithdraw(0) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}