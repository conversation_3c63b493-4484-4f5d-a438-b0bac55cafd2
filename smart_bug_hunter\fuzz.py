"""
Combinatorial fuzzing implementation using allpairspy and Foundry.

Generates pairwise or t-wise input pools and executes them via Foundry fuzz testing.
"""

import json
import subprocess
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from allpairspy import AllPairs
from rich.console import Console
from rich.progress import Progress, TaskID

from .models import FuzzResults, TestPool, TestResult, FunctionSignature, ContractAnalysis
from .contract_analyzer import ContractAnalyzer

console = Console()


class FuzzRunner:
    """Handles combinatorial fuzzing of Solidity contracts."""
    
    def __init__(
        self,
        contract_path: Path,
        schema_path: Optional[Path] = None,
        strength: int = 2,
        output_dir: Optional[Path] = None,
        verbose: bool = False,
    ):
        self.contract_path = contract_path
        self.schema_path = schema_path
        self.strength = strength
        self.output_dir = output_dir or Path("./output")
        self.verbose = verbose
        self.analyzer = ContractAnalyzer(verbose=verbose)
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def run(self) -> FuzzResults:
        """Execute the complete fuzzing process."""
        start_time = time.time()
        
        if self.verbose:
            console.print(f"🔍 Starting fuzzing analysis of {self.contract_path}")
        
        # Analyze contract to get function signatures
        contract_analysis = self.analyzer.analyze_contract(self.contract_path)
        if not contract_analysis.is_valid:
            raise RuntimeError(f"Contract analysis failed: {contract_analysis.analysis_errors}")
        
        # Get fuzzable functions
        fuzzable_functions = contract_analysis.get_fuzzable_functions()
        if not fuzzable_functions:
            console.print("⚠️  No fuzzable functions found (public/external with parameters)")
            return FuzzResults(
                contract_path=self.contract_path,
                total_pools=0,
                execution_time=time.time() - start_time
            )
        
        if self.verbose:
            console.print(f"Found {len(fuzzable_functions)} fuzzable functions")
        
        # Generate test pools for each function
        all_results = FuzzResults(
            contract_path=self.contract_path,
            total_pools=0,
            execution_time=0.0
        )
        
        for func in fuzzable_functions:
            if self.verbose:
                console.print(f"Fuzzing function: {func.signature}")
            
            func_results = self._fuzz_function(func, contract_analysis)
            
            # Merge results
            all_results.total_pools += func_results.total_pools
            all_results.passed_pools.extend(func_results.passed_pools)
            all_results.failed_pools.extend(func_results.failed_pools)
            all_results.error_pools.extend(func_results.error_pools)
        
        all_results.execution_time = time.time() - start_time
        
        # Save results
        self._save_results(all_results)
        
        return all_results
    
    def _fuzz_function(self, func: FunctionSignature, contract_analysis: ContractAnalysis) -> FuzzResults:
        """Fuzz a specific function."""
        # Load or generate input schema
        input_schema = self._get_input_schema(func)
        
        # Generate combinatorial test pools
        pools = self._generate_test_pools(func, input_schema)
        
        if not pools:
            return FuzzResults(
                contract_path=self.contract_path,
                total_pools=0
            )
        
        # Execute pools
        results = FuzzResults(
            contract_path=self.contract_path,
            total_pools=len(pools)
        )
        
        with Progress() as progress:
            task = progress.add_task(f"Fuzzing {func.name}", total=len(pools))
            
            for pool in pools:
                result = self._execute_pool(pool, func, contract_analysis)
                
                if result.result == TestResult.PASS:
                    results.passed_pools.append(result)
                elif result.result == TestResult.FAIL:
                    results.failed_pools.append(result)
                else:
                    results.error_pools.append(result)
                
                progress.advance(task)
        
        return results
    
    def _get_input_schema(self, func: FunctionSignature) -> Dict[str, List[Any]]:
        """Get input schema for function parameters."""
        if self.schema_path and self.schema_path.exists():
            with open(self.schema_path) as f:
                schema = json.load(f)
                if func.name in schema:
                    return schema[func.name]
        
        # Generate default schema based on parameter types
        schema = {}
        for param in func.inputs:
            param_name = param["name"]
            param_type = param["type"]
            schema[param_name] = self._generate_default_values(param_type)
        
        return schema
    
    def _generate_default_values(self, solidity_type: str) -> List[Any]:
        """Generate default test values for Solidity types."""
        if solidity_type.startswith("uint"):
            return [0, 1, 100, 1000, 2**256 - 1]  # Edge cases for uints
        elif solidity_type.startswith("int"):
            return [-1000, -1, 0, 1, 1000]
        elif solidity_type == "bool":
            return [True, False]
        elif solidity_type == "address":
            return [
                "0x0000000000000000000000000000000000000000",
                "0x1111111111111111111111111111111111111111",
                "0xffffffffffffffffffffffffffffffffffffffff",
            ]
        elif solidity_type == "string":
            return ["", "test", "a" * 100, "special!@#$%"]
        elif solidity_type == "bytes":
            return ["0x", "0x00", "0xff", "0x" + "aa" * 32]
        else:
            # Default fallback
            return [0, 1, -1, 100, "test"]
    
    def _generate_test_pools(self, func: FunctionSignature, schema: Dict[str, List[Any]]) -> List[TestPool]:
        """Generate combinatorial test pools using AllPairs."""
        if not schema:
            return []
        
        param_names = list(schema.keys())
        param_values = [schema[name] for name in param_names]
        
        if len(param_values) < 2:
            # For single parameter, just test all values
            pools = []
            for i, value in enumerate(param_values[0]):
                pools.append(TestPool(
                    id=i,
                    parameters=[value],
                    parameter_names=param_names
                ))
            return pools
        
        # Generate combinatorial pairs
        pools = []
        for i, combination in enumerate(AllPairs(param_values)):
            pools.append(TestPool(
                id=i,
                parameters=list(combination),
                parameter_names=param_names
            ))
        
        return pools
    
    def _execute_pool(self, pool: TestPool, func: FunctionSignature, contract_analysis: ContractAnalysis) -> TestPool:
        """Execute a single test pool using Foundry."""
        start_time = time.time()
        
        try:
            # Create a temporary test file for this pool
            test_content = self._generate_foundry_test(pool, func, contract_analysis)
            test_file = self.output_dir / f"FuzzTest_{func.name}_{pool.id}.t.sol"
            
            with open(test_file, "w") as f:
                f.write(test_content)
            
            # Run forge test
            cmd = [
                "forge", "test",
                "--match-path", str(test_file),
                "-vvv"
            ]
            
            if self.verbose:
                console.print(f"Executing: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                cwd=self.contract_path.parent if self.contract_path.is_file() else self.contract_path,
                capture_output=True,
                text=True,
                timeout=30  # 30 second timeout per test
            )
            
            pool.execution_time = time.time() - start_time
            
            if result.returncode == 0:
                pool.result = TestResult.PASS
            else:
                pool.result = TestResult.FAIL
                pool.error_message = result.stderr or result.stdout
            
            # Clean up test file
            test_file.unlink(missing_ok=True)
            
        except subprocess.TimeoutExpired:
            pool.result = TestResult.TIMEOUT
            pool.error_message = "Test execution timed out"
            pool.execution_time = time.time() - start_time
        except Exception as e:
            pool.result = TestResult.ERROR
            pool.error_message = str(e)
            pool.execution_time = time.time() - start_time
        
        return pool
    
    def _generate_foundry_test(self, pool: TestPool, func: FunctionSignature, contract_analysis: ContractAnalysis) -> str:
        """Generate Foundry test code for a test pool."""
        contract_name = contract_analysis.contract_name
        
        # Format parameters for the function call
        formatted_params = []
        for i, (param, value) in enumerate(zip(func.inputs, pool.parameters)):
            param_type = param["type"]
            if param_type == "string":
                formatted_params.append(f'"{value}"')
            elif param_type == "address":
                formatted_params.append(f'address({value})')
            elif param_type == "bool":
                formatted_params.append(str(value).lower())
            else:
                formatted_params.append(str(value))
        
        params_str = ", ".join(formatted_params)
        
        test_content = f'''// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../{contract_analysis.contract_path.name}";

contract FuzzTest_{func.name}_{pool.id} is Test {{
    {contract_name} target;
    
    function setUp() public {{
        target = new {contract_name}();
    }}
    
    function test_{func.name}_pool_{pool.id}() public {{
        // Test pool {pool.id}: {dict(zip(pool.parameter_names, pool.parameters))}
        try target.{func.name}({params_str}) {{
            // Function executed successfully
        }} catch {{
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }}
    }}
}}'''
        
        return test_content
    
    def _save_results(self, results: FuzzResults) -> None:
        """Save fuzzing results to JSON file."""
        output_file = self.output_dir / "fuzz_results.json"
        
        results_dict = {
            "contract_path": str(results.contract_path),
            "total_pools": results.total_pools,
            "passed_pools": [pool.to_dict() for pool in results.passed_pools],
            "failed_pools": [pool.to_dict() for pool in results.failed_pools],
            "error_pools": [pool.to_dict() for pool in results.error_pools],
            "execution_time": results.execution_time,
            "success_rate": results.success_rate,
            "has_failures": results.has_failures,
        }
        
        with open(output_file, "w") as f:
            json.dump(results_dict, f, indent=2)
        
        if self.verbose:
            console.print(f"💾 Fuzzing results saved to {output_file}")
