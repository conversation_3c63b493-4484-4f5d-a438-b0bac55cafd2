// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_transfer_8 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_transfer_pool_8() public {
        // Test pool 8: {'to': '0xffffffffffffffffffffffffffffffffffffffff', 'amount': 100}
        try target.transfer(address(0xffffffffffffffffffffffffffffffffffffffff), 100) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}