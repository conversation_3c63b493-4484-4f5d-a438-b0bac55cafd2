"""
Tests for the models module.
"""

import pytest
from pathlib import Path
from smart_bug_hunter.models import (
    FunctionSignature, TestPool, TestResult, FuzzResults,
    Invariant, InvariantResults, ContractAnalysis
)


def test_function_signature():
    """Test FunctionSignature model."""
    func = FunctionSignature(
        name="transfer",
        inputs=[{"name": "to", "type": "address"}, {"name": "amount", "type": "uint256"}],
        outputs=[],
        visibility="public",
        stateMutability="nonpayable"
    )
    
    assert func.signature == "transfer(address,uint256)"


def test_test_pool():
    """Test TestPool model."""
    pool = TestPool(
        id=1,
        parameters=["0x123", 100],
        parameter_names=["to", "amount"]
    )
    
    assert pool.result == TestResult.PASS
    assert pool.to_dict()["id"] == 1
    assert pool.to_dict()["parameters"] == ["0x123", 100]


def test_fuzz_results():
    """Test FuzzResults model."""
    results = FuzzResults(
        contract_path=Path("test.sol"),
        total_pools=10
    )
    
    # Add some test pools
    passed_pool = TestPool(id=1, parameters=[1], parameter_names=["x"])
    failed_pool = TestPool(id=2, parameters=[2], parameter_names=["x"])
    failed_pool.result = TestResult.FAIL
    
    results.passed_pools.append(passed_pool)
    results.failed_pools.append(failed_pool)
    
    assert results.has_failures == True
    assert results.success_rate == 0.1  # 1 passed out of 10 total
    
    failing_params = results.get_failing_parameters()
    assert len(failing_params) == 1
    assert failing_params[0]["pool_id"] == 2


def test_invariant():
    """Test Invariant model."""
    inv = Invariant(
        id="test_1",
        function_name="transfer",
        type="precondition",
        description="Amount must be positive",
        solidity_assertion="require(amount > 0, 'Amount must be positive');"
    )
    
    assert inv.to_dict()["id"] == "test_1"
    assert inv.to_dict()["function_name"] == "transfer"


def test_invariant_results():
    """Test InvariantResults model."""
    results = InvariantResults(
        contract_path=Path("test.sol"),
        total_invariants=5
    )
    
    # Add some invariants
    verified_inv = Invariant(
        id="verified_1",
        function_name="test",
        type="precondition",
        description="Test",
        solidity_assertion="require(true);"
    )
    verified_inv.is_verified = True
    
    violated_inv = Invariant(
        id="violated_1",
        function_name="test",
        type="postcondition",
        description="Test",
        solidity_assertion="assert(false);"
    )
    violated_inv.is_verified = False
    
    results.verified_invariants.append(verified_inv)
    results.violations.append(violated_inv)
    
    assert results.has_violations == True
    assert results.verification_rate == 0.2  # 1 verified out of 5 total


def test_contract_analysis():
    """Test ContractAnalysis model."""
    analysis = ContractAnalysis(
        contract_path=Path("test.sol"),
        contract_name="TestContract"
    )
    
    # Add a function
    func = FunctionSignature(
        name="publicFunc",
        inputs=[{"name": "x", "type": "uint256"}],
        outputs=[],
        visibility="public",
        stateMutability="nonpayable"
    )
    analysis.functions.append(func)
    
    # Add a private function
    private_func = FunctionSignature(
        name="privateFunc",
        inputs=[],
        outputs=[],
        visibility="private",
        stateMutability="pure"
    )
    analysis.functions.append(private_func)
    
    assert analysis.is_valid == True
    
    fuzzable = analysis.get_fuzzable_functions()
    assert len(fuzzable) == 1
    assert fuzzable[0].name == "publicFunc"
