@echo off
REM Smart Bug Hunter Demo Script for Windows
REM This script demonstrates the complete analysis of a vulnerable Bank contract

echo 🚀 Smart Bug Hunter Demo
echo ========================
echo.

REM Check if we're in the right directory
if not exist "Bank.sol" (
    echo ❌ Error: Bank.sol not found. Please run this script from the demo\ directory.
    exit /b 1
)

REM Check if Foundry is installed
where forge >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Foundry not found. Please install Foundry first:
    echo    curl -L https://foundry.paradigm.xyz ^| bash
    echo    foundryup
    exit /b 1
)

REM Check if smart-bug-hunter is installed
where smart-bug-hunter >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: smart-bug-hunter not found. Please install it first:
    echo    pip install smart-bug-hunter
    exit /b 1
)

REM Initialize Foundry project if needed
if not exist "lib\forge-std\src\Test.sol" (
    echo 📦 Initializing Foundry project...
    forge init --no-git --force .
    echo.
)

REM Set OpenAI API key if not set
if "%OPENAI_API_KEY%"=="" (
    echo ⚠️  Warning: OPENAI_API_KEY not set. Invariant verification will be skipped.
    echo    To enable full analysis, set your OpenAI API key:
    echo    set OPENAI_API_KEY=your-api-key-here
    echo.
    
    REM Run only fuzzing
    echo 🔍 Running fuzzing analysis only...
    smart-bug-hunter fuzz Bank.sol --schema schema.json --verbose
) else (
    REM Run complete analysis
    echo 🔍 Running complete analysis (fuzzing + invariant verification)...
    smart-bug-hunter all Bank.sol --schema schema.json --verbose
)

echo.
echo ✅ Demo completed!
echo.
echo 📄 Check the generated report:
echo    type output\smart_bug_hunter_report.md
echo.
echo 📊 View detailed results:
echo    dir output\
echo.
echo 🔍 Expected findings:
echo    - Integer overflow vulnerabilities
echo    - Reentrancy issues
echo    - Access control problems
echo    - Improper balance validation
echo.
