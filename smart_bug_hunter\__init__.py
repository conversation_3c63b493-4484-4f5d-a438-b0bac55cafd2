"""
Smart Bug Hunter - Combinatorial fuzzing and formal verification for Solidity contracts.

A CLI tool that combines:
- Combinatorial group-testing fuzzing using Foundry
- LLM-generated invariants with SMT proof verification
- Unified reporting of vulnerabilities and violations
"""

__version__ = "0.1.0"
__author__ = "Smart Bug Hunter Team"
__email__ = "<EMAIL>"

from .cli import app

__all__ = ["app"]
