// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_batchDeposit_2 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_batchDeposit_pool_2() public {
        // Test pool 2: {'amounts': [**********, **********, **********]}
        try target.batchDeposit([**********, **********, **********]) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}