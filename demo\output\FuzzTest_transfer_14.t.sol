// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_transfer_14 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_transfer_pool_14() public {
        // Test pool 14: {'to': '0xffffffffffffffffffffffffffffffffffffffff', 'amount': 115792089237316195423570985008687907853269984665640564039457584007913129639935}
        try target.transfer(address(0xffffffffffffffffffffffffffffffffffffffff), 115792089237316195423570985008687907853269984665640564039457584007913129639935) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}