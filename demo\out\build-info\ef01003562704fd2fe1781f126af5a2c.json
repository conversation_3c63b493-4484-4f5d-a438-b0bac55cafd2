{"id": "ef01003562704fd2fe1781f126af5a2c", "source_id_to_path": {"0": "Bank.sol", "1": "lib/forge-std/src/Base.sol", "2": "lib/forge-std/src/Script.sol", "3": "lib/forge-std/src/StdAssertions.sol", "4": "lib/forge-std/src/StdChains.sol", "5": "lib/forge-std/src/StdCheats.sol", "6": "lib/forge-std/src/StdConstants.sol", "7": "lib/forge-std/src/StdError.sol", "8": "lib/forge-std/src/StdInvariant.sol", "9": "lib/forge-std/src/StdJson.sol", "10": "lib/forge-std/src/StdMath.sol", "11": "lib/forge-std/src/StdStorage.sol", "12": "lib/forge-std/src/StdStyle.sol", "13": "lib/forge-std/src/StdToml.sol", "14": "lib/forge-std/src/StdUtils.sol", "15": "lib/forge-std/src/Test.sol", "16": "lib/forge-std/src/Vm.sol", "17": "lib/forge-std/src/console.sol", "18": "lib/forge-std/src/console2.sol", "19": "lib/forge-std/src/interfaces/IERC1155.sol", "20": "lib/forge-std/src/interfaces/IERC165.sol", "21": "lib/forge-std/src/interfaces/IERC20.sol", "22": "lib/forge-std/src/interfaces/IERC4626.sol", "23": "lib/forge-std/src/interfaces/IERC6909.sol", "24": "lib/forge-std/src/interfaces/IERC721.sol", "25": "lib/forge-std/src/interfaces/IERC7540.sol", "26": "lib/forge-std/src/interfaces/IERC7575.sol", "27": "lib/forge-std/src/interfaces/IMulticall3.sol", "28": "lib/forge-std/src/safeconsole.sol", "29": "lib/forge-std/test/CommonBase.t.sol", "30": "lib/forge-std/test/StdAssertions.t.sol", "31": "lib/forge-std/test/StdChains.t.sol", "32": "lib/forge-std/test/StdCheats.t.sol", "33": "lib/forge-std/test/StdConstants.t.sol", "34": "lib/forge-std/test/StdError.t.sol", "35": "lib/forge-std/test/StdJson.t.sol", "36": "lib/forge-std/test/StdMath.t.sol", "37": "lib/forge-std/test/StdStorage.t.sol", "38": "lib/forge-std/test/StdStyle.t.sol", "39": "lib/forge-std/test/StdToml.t.sol", "40": "lib/forge-std/test/StdUtils.t.sol", "41": "lib/forge-std/test/Vm.t.sol", "42": "lib/forge-std/test/compilation/CompilationScript.sol", "43": "lib/forge-std/test/compilation/CompilationScriptBase.sol", "44": "lib/forge-std/test/compilation/CompilationTest.sol", "45": "lib/forge-std/test/compilation/CompilationTestBase.sol", "46": "script/Counter.s.sol", "47": "src/Counter.sol", "48": "test/Counter.t.sol"}, "language": "Solidity"}