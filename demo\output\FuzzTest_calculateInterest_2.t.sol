// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_calculateInterest_2 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_calculateInterest_pool_2() public {
        // Test pool 2: {'principal': 1000, 'rate': 100, 'time': 0}
        try target.calculateInterest(1000, 100, 0) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}