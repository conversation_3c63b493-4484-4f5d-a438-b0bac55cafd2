// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_transfer_2 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_transfer_pool_2() public {
        // Test pool 2: {'to': '0xffffffffffffffffffffffffffffffffffffffff', 'amount': 0}
        try target.transfer(address(0xffffffffffffffffffffffffffffffffffffffff), 0) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}