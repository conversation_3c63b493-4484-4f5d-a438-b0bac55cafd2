# Smart Bug Hunter Demo

This demo showcases the smart-bug-hunter tool analyzing a vulnerable Solidity contract.

## Quick Start

```bash
cd demo/
chmod +x run_demo.sh
./run_demo.sh
```

## What's Included

### Bank.sol
A deliberately vulnerable smart contract containing:
- **Integer overflow** in deposit function
- **Reentrancy vulnerability** in withdraw function  
- **Access control issues** in admin functions
- **Improper balance validation** in transfer function
- **DoS vulnerability** in batch operations
- **Precision loss** in interest calculations

### schema.json
Input schema defining test values for each function parameter, including edge cases like:
- Zero values
- Maximum uint256 values
- Empty arrays
- Invalid addresses

### Expected Findings

The tool should detect:

1. **Fuzzing Issues:**
   - Functions failing with large input values (overflow)
   - Transfer function allowing negative balances
   - Batch deposit DoS with large arrays
   - Access control bypass in addAdmin

2. **Invariant Violations:**
   - Balance consistency violations
   - Unauthorized state changes
   - Mathematical property violations
   - Access control invariants

## Manual Analysis

You can also run individual commands:

```bash
# Fuzzing only
smart-bug-hunter fuzz Bank.sol --schema schema.json

# Invariant verification only (requires OpenAI API key)
smart-bug-hunter prove Bank.sol --openai-key YOUR_KEY

# Complete analysis
smart-bug-hunter all Bank.sol --schema schema.json --openai-key YOUR_KEY
```

## Output Files

After running the demo, check:
- `output/smart_bug_hunter_report.md` - Unified analysis report
- `output/fuzz_results.json` - Detailed fuzzing results
- `output/invariant_results.json` - Invariant verification results

## Prerequisites

1. **Foundry** - Install from https://foundry.paradigm.xyz
2. **Python 3.11+** with smart-bug-hunter package
3. **OpenAI API Key** (optional, for invariant verification)

```bash
# Install Foundry
curl -L https://foundry.paradigm.xyz | bash
foundryup

# Install smart-bug-hunter
pip install smart-bug-hunter

# Set OpenAI API key (optional)
export OPENAI_API_KEY="your-api-key-here"
```
