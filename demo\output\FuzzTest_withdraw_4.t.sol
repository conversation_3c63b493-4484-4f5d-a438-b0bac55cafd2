// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../Bank.sol";

contract FuzzTest_withdraw_4 is Test {
    Bank target;
    
    function setUp() public {
        target = new Bank();
    }
    
    function test_withdraw_pool_4() public {
        // Test pool 4: {'amount': 115792089237316195423570985008687907853269984665640564039457584007913129639935}
        try target.withdraw(115792089237316195423570985008687907853269984665640564039457584007913129639935) {
            // Function executed successfully
        } catch {
            // Function reverted - this might indicate a bug
            fail("Function reverted unexpectedly");
        }
    }
}